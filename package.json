{"name": "claude-dev", "displayName": "Cline", "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.", "version": "3.19.7", "icon": "assets/icons/icon.png", "engines": {"vscode": "^1.84.0"}, "author": {"name": "Cline Bot Inc."}, "license": "Apache-2.0", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/cline/cline"}, "homepage": "https://cline.bot", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama"], "activationEvents": ["onLanguage", "onStartupFinished", "workspaceContains:evals.env"], "main": "./dist/extension.js", "contributes": {"walkthroughs": [{"id": "ClineWalkthrough", "title": "Meet <PERSON><PERSON>, your new coding partner", "description": "Cline codes like a developer because it thinks like one. Here are 5 ways to put it to work:", "steps": [{"id": "welcome", "title": "Start with a Goal, Not Just a Prompt", "description": "Tell Cline what you want to achieve. It plans, asks, and then codes, like a true partner.", "media": {"markdown": "walkthrough/step1.md"}}, {"id": "learn", "title": "Let Cline Learn Your Codebase", "description": "Point Cline to your project. It builds understanding to make smart, context-aware changes.", "media": {"markdown": "walkthrough/step2.md"}}, {"id": "advanced-features", "title": "Always Use the Best AI Models", "description": "Cline empowers you with State-of-the-Art AI, connecting to top models (Anthropic, Gemini, OpenAI & more).", "media": {"markdown": "walkthrough/step3.md"}}, {"id": "mcp", "title": "Extend with Powerful Tools (MCP)", "description": "Connect to databases, APIs, or discover new capabilities in the MCP Marketplace.", "media": {"markdown": "walkthrough/step4.md"}}, {"id": "getting-started", "title": "You're Always in Control", "description": "Review C<PERSON>'s plans and diffs. Approve changes before they happen. No surprises.", "media": {"markdown": "walkthrough/step5.md"}, "content": {"path": "walkthrough/step5.md"}}]}], "viewsContainers": {"activitybar": [{"id": "claude-dev-ActivityBar", "title": "Cline (⌘+')", "icon": "assets/icons/icon.svg", "when": "isMac"}, {"id": "claude-dev-ActivityBar", "title": "Cline (Ctrl+')", "icon": "assets/icons/icon.svg", "when": "!isMac"}]}, "views": {"claude-dev-ActivityBar": [{"type": "webview", "id": "claude-dev.<PERSON>", "name": ""}]}, "commands": [{"command": "cline.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "cline.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "cline.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "cline.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "cline.accountButtonClicked", "title": "Account", "icon": "$(account)"}, {"command": "cline.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "cline.openInNewTab", "title": "Open In New Tab", "category": "Cline"}, {"command": "cline.dev.testParsers", "title": "Test Tree-sitter Pa<PERSON><PERSON>", "category": "Cline", "when": "cline.isDevMode"}, {"command": "cline.dev.createTestTasks", "title": "Create Test Tasks", "category": "Cline", "when": "cline.isDevMode"}, {"command": "cline.addToChat", "title": "Add to Cline", "category": "Cline"}, {"command": "cline.addTerminalOutputToChat", "title": "Add to Cline", "category": "Cline"}, {"command": "cline.focusChatInput", "title": "Jump to Chat Input", "category": "Cline"}, {"command": "cline.generateGitCommitMessage", "title": "Generate Commit Message with Cline", "category": "Cline", "icon": "$(robot)"}, {"command": "cline.explainCode", "title": "Explain with <PERSON><PERSON>", "category": "Cline"}, {"command": "cline.improveCode", "title": "Improve with Cline", "category": "Cline"}, {"command": "cline.openWalkthrough", "title": "Open Walkthrough", "category": "Cline"}, {"command": "qax-code.toggleAutocomplete", "title": "Toggle QAX Autocomplete", "category": "QAX"}, {"command": "qax-code.trackAcceptedSuggestion", "title": "Track Accepted Suggestion", "category": "QAX"}], "configuration": {"title": "Cline", "properties": {"cline.autocomplete.enabled": {"type": "boolean", "default": false, "description": "Enable QAX autocomplete functionality"}, "cline.autocomplete.provider": {"type": "string", "enum": ["openai", "fim"], "default": "openai", "description": "Provider type for autocomplete service"}, "cline.autocomplete.apiBaseUrl": {"type": "string", "default": "https://api.openrouter.ai/api/v1", "description": "Base URL for the autocomplete API (supports any OpenAI-compatible endpoint)"}, "cline.autocomplete.modelId": {"type": "string", "default": "google/gemini-2.5-flash-preview-05-20", "description": "Model ID to use for autocomplete"}, "cline.autocomplete.maxTokens": {"type": "number", "default": 1000, "minimum": 1, "maximum": 10000, "description": "Maximum number of tokens for completion"}, "cline.autocomplete.temperature": {"type": "number", "default": 0.1, "minimum": 0, "maximum": 2, "description": "Temperature for completion generation"}, "cline.autocomplete.requestTimeoutMs": {"type": "number", "default": 30000, "minimum": 1000, "maximum": 300000, "description": "Request timeout in milliseconds"}, "cline.autocomplete.usePromptCache": {"type": "boolean", "default": false, "description": "Whether to use prompt caching"}, "cline.autocomplete.customHeaders": {"type": "object", "default": {}, "description": "Custom headers for API requests"}, "cline.autocomplete.debounceMs": {"type": "number", "default": 300, "minimum": 0, "maximum": 5000, "description": "Debounce time in milliseconds for autocomplete requests"}, "cline.autocomplete.filter.enabled": {"type": "boolean", "default": true, "description": "Whether to enable completion filtering"}, "cline.autocomplete.filter.filterWhitespaceOnly": {"type": "boolean", "default": true, "description": "Whether to filter out completions that are just whitespace"}, "cline.autocomplete.filter.filterRepeatedContent": {"type": "boolean", "default": true, "description": "Whether to filter out completions that repeat existing content"}, "cline.autocomplete.filter.filterStrings": {"type": "array", "items": {"type": "string"}, "default": ["{{FILL_HERE}}", "{{TODO}}", "{{PLACEHOLDER}}"], "description": "Specific strings to filter out from completions"}, "cline.autocomplete.fim.baseUrl": {"type": "string", "default": "", "description": "Base URL for FIM (Fill in the Middle) API"}, "cline.memory.enabled": {"type": "boolean", "default": true, "description": "Enable memory functionality to learn user preferences and habits"}, "cline.memory.minInputLength": {"type": "number", "default": 10, "minimum": 5, "maximum": 100, "description": "Minimum input length to process for memory extraction"}, "cline.memory.maxEntriesPerCategory": {"type": "number", "default": 20, "minimum": 5, "maximum": 100, "description": "Maximum number of memory entries to keep per category"}, "cline.memory.confidenceThreshold": {"type": "number", "default": 0.6, "minimum": 0.1, "maximum": 1, "description": "Minimum confidence threshold for saving memory entries (0.1-1.0)"}, "cline.memory.debounceMs": {"type": "number", "default": 2000, "minimum": 500, "maximum": 10000, "description": "Debounce time in milliseconds for processing user input"}}}, "keybindings": [{"command": "cline.addToChat", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "editorHasSelection"}, {"command": "cline.generateGitCommitMessage", "when": "scmProvider == git"}, {"command": "cline.focusChatInput", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "!editorHasSelection"}], "menus": {"view/title": [{"command": "cline.plusButtonClicked", "group": "navigation@1", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.mcpButtonClicked", "group": "navigation@2", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.historyButtonClicked", "group": "navigation@3", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.popoutButtonClicked", "group": "navigation@4", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.accountButtonClicked", "group": "navigation@5", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.settingsButtonClicked", "group": "navigation@6", "when": "view == claude-dev.<PERSON>"}], "editor/title": [{"command": "cline.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}, {"command": "cline.mcpButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}, {"command": "cline.historyButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}, {"command": "cline.popoutButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}, {"command": "cline.accountButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}, {"command": "cline.settingsButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == claude-dev.TabPanelProvider"}], "editor/context": [{"command": "cline.addToChat", "group": "navigation", "when": "editorHasSelection"}], "terminal/context": [{"command": "cline.addTerminalOutputToChat", "group": "navigation"}], "scm/title": [{"command": "cline.generateGitCommitMessage", "group": "navigation", "when": "scmProvider == git"}], "commandPalette": [{"command": "cline.generateGitCommitMessage", "when": "scmProvider == git"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "compile-standalone": "npm run check-types && npm run lint && node esbuild.js --standalone", "postcompile-standalone": "node scripts/package-standalone.mjs", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run check-types && npm run build:webview && npm run lint && node esbuild.js --production", "protos": "node scripts/build-proto.mjs && node scripts/generate-server-setup.mjs && node scripts/generate-host-bridge-client.mjs", "postprotos": "prettier src/shared/proto src/core/controller src/hosts/ webview-ui/src/services src/generated --write --log-level warn", "clean": "rimraf dist dist-standalone webview-ui/build src/generated out/", "compile-tests": "node ./scripts/build-tests.js", "watch-tests": "tsc -p . -w --outDir out", "check-types": "npm run protos && npx tsc --noEmit && cd webview-ui && npx tsc -b --noEmit", "lint": "eslint src --ext ts && eslint webview-ui/src --ext ts && buf lint && cd webview-ui && npm run lint", "format": "prettier . --check", "format:fix": "prettier . --write", "pretest": "npm run compile && npm run compile-tests && npm run compile-standalone && npm run lint", "test": "npm-run-all test:unit test:integration", "test:ci": "node scripts/test-ci.js", "test:integration": "vscode-test", "test:unit": "TS_NODE_PROJECT='./tsconfig.unit-test.json' mocha", "test:coverage": "vscode-test --coverage", "e2e": "playwright test -c playwright.config.ts", "test:e2e": "playwright install && vsce package --no-dependencies --out dist/e2e.vsix && node src/test/e2e/utils/build.js && playwright test", "install:all": "npm install && cd webview-ui && npm install", "dev:webview": "cd webview-ui && npm run dev", "build:webview": "cd webview-ui && npm run build", "test:webview": "cd webview-ui && npm run test", "publish:marketplace": "vsce publish && ovsx publish", "publish:marketplace:prerelease": "vsce publish --pre-release && ovsx publish --pre-release", "prepare": "husky", "changeset": "changeset", "version-packages": "changeset version", "docs": "cd docs && mintlify dev", "docs:check-links": "cd docs && mintlify broken-links", "docs:rename-file": "cd docs && mintlify rename", "report-issue": "node scripts/report-issue.js", "build:qax": "node qax/scripts/build-qax.js", "build:qax:dev": "node qax/scripts/build-qax.js --env=dev", "build:qax:test": "node qax/scripts/build-qax.js --env=test", "build:qax:prod": "node qax/scripts/build-qax.js --env=prod", "clean:qax": "node qax/scripts/build-qax.js --clean", "test:qax:files": "node qax/scripts/test-file-processing.js", "verify:qax": "node qax/scripts/verify-no-modifications.js"}, "lint-staged": {"*": ["prettier --write --ignore-unknown --log-level=log"]}, "devDependencies": {"@bufbuild/buf": "^1.54.0", "@changesets/cli": "^2.27.12", "@types/chai": "^5.0.1", "@types/clone-deep": "^4.0.4", "@types/diff": "^5.2.1", "@types/get-folder-size": "^3.0.4", "@types/mocha": "^10.0.7", "@types/node": "20.x", "@types/pdf-parse": "^1.1.4", "@types/proxyquire": "^1.3.31", "@types/should": "^11.2.0", "@types/sinon": "^17.0.4", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.18.0", "@typescript-eslint/utils": "^8.33.0", "@vitest/ui": "^3.2.4", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.6.0", "chai": "^4.3.10", "chalk": "^5.3.0", "esbuild": "^0.25.0", "eslint": "^8.57.0", "eslint-plugin-eslint-rules": "file:eslint-rules", "grpc-tools": "^1.13.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "minimatch": "^3.0.3", "mintlify": "^4.0.515", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "protoc-gen-ts": "^0.8.7", "proxyquire": "^2.1.3", "rimraf": "^6.0.1", "should": "^13.2.3", "sinon": "^19.0.5", "ts-node": "^10.9.2", "ts-proto": "^2.6.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5", "vitest": "^3.2.4"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.6.4", "@aws-sdk/client-bedrock-runtime": "^3.840.0", "@aws-sdk/credential-providers": "^3.840.0", "@bufbuild/protobuf": "^2.2.5", "@cerebras/cerebras_cloud_sdk": "^1.35.0", "@google-cloud/vertexai": "^1.9.3", "@google/genai": "1.0.0", "@grpc/grpc-js": "^1.9.15", "@grpc/reflection": "^1.0.4", "@mistralai/mistralai": "^1.5.0", "@modelcontextprotocol/sdk": "^1.11.1", "@opentelemetry/api": "^1.4.1", "@opentelemetry/exporter-trace-otlp-http": "^0.39.1", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-node": "^0.39.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.30.0", "@playwright/test": "^1.53.2", "@sentry/browser": "^9.12.0", "@streamparser/json": "^0.0.22", "@vscode/codicons": "^0.0.36", "archiver": "^7.0.1", "axios": "^1.8.2", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "chrome-launcher": "^1.1.2", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "diff": "^5.2.0", "exceljs": "^4.4.0", "execa": "^9.5.2", "fast-deep-equal": "^3.1.3", "firebase": "^11.2.0", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "globby": "^14.0.2", "grpc-health-check": "^2.0.2", "iconv-lite": "^0.6.3", "ignore": "^7.0.3", "image-size": "^2.0.2", "isbinaryfile": "^5.0.2", "jschardet": "^3.1.4", "jwt-decode": "^4.0.0", "lru-cache": "^10.0.0", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "nice-grpc": "^2.1.12", "ollama": "^0.5.13", "open": "^10.1.2", "open-graph-scraper": "^6.9.0", "openai": "^4.83.0", "os-name": "^6.0.0", "p-timeout": "^6.1.4", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "posthog-node": "^4.8.1", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "strip-ansi": "^7.1.0", "tree-sitter-wasms": "^0.1.11", "ts-morph": "^25.0.1", "turndown": "^7.2.0", "vscode-uri": "^3.1.0", "web-tree-sitter": "^0.22.6", "zod": "^3.24.2"}}
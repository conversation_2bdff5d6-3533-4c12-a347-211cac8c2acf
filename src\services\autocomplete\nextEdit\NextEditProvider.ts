import * as vscode from "vscode"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../api"
import { AutocompleteConfigManager } from "../AutocompleteConfigManager"
import { NextEditService } from "./NextEditService"
import { DEFAULT_NEXT_EDIT_CONFIG } from "./types/NextEditTypes"
import { ApiHandlerManager } from "../shared/ApiHandlerManager"

/**
 * Register and manage Next Edit functionality
 */
export function registerNextEdit(context: vscode.ExtensionContext): void {
	let nextEditService: NextEditService | null = null
	let isCurrentlyEnabled = false
	let commandsRegistered = false

	// Function to check configuration and update service
	const checkAndUpdateService = () => {
		try {
			const configManager = AutocompleteConfigManager.instance
			const isEnabled = configManager.isEnabled()
			const hasApiKey = configManager.hasApiKey()
			const shouldBeEnabled = isEnabled && hasApiKey

			// Only take action if the state has changed
			if (shouldBeEnabled !== isCurrentlyEnabled) {
				if (nextEditService) {
					nextEditService.dispose()
					nextEditService = null
				}

				if (shouldBeEnabled) {
					nextEditService = setupNextEdit(context, !commandsRegistered)
					commandsRegistered = true
				}

				isCurrentlyEnabled = shouldBeEnabled
			}
		} catch (error) {
			console.error("🚀❌ NextEditProvider error:", error)
			return
		}
	}

	// Initial check
	checkAndUpdateService()

	// Monitor autocomplete configuration changes to update API handler
	const configWatcher = vscode.workspace.onDidChangeConfiguration((event) => {
		if (event.affectsConfiguration("qax-code.autocomplete")) {
			// 通知 ApiHandlerManager 重新创建 handlers
			const apiHandlerManager = ApiHandlerManager.getInstance()
			apiHandlerManager.recreateHandlers()

			// 如果服务已启用，更新其 API handler
			if (nextEditService) {
				const newApiHandler = getAutocompleteApiHandler()
				nextEditService.updateApiHandler(newApiHandler)
			}

			// 重新检查配置
			checkAndUpdateService()
		}
	})

	// Clean up on extension deactivation
	context.subscriptions.push(configWatcher, {
		dispose: () => {
			if (nextEditService) {
				nextEditService.dispose()
				nextEditService = null
			}
		},
	})
}

/**
 * Get API handler from shared manager (same as autocomplete)
 */
function getAutocompleteApiHandler(): ApiHandler | null {
	const apiHandlerManager = ApiHandlerManager.getInstance()
	const handler = apiHandlerManager.getApiHandler()
	console.log("🚀🔍 NextEdit: Got API handler from manager:", !!handler)
	return handler
}

/**
 * Setup Next Edit service
 */
function setupNextEdit(context: vscode.ExtensionContext, registerCommands: boolean = true): NextEditService {
	// 使用 autocomplete 的 API handler
	const apiHandler = getAutocompleteApiHandler()

	// 强制重新创建 NextEditService 实例以确保正确初始化
	NextEditService.dispose()
	const nextEditService = NextEditService.getInstance(DEFAULT_NEXT_EDIT_CONFIG, apiHandler)
	console.log("🚀🔍 NextEdit: Service initialized with handler:", !!apiHandler)

	// Register commands only once
	if (registerCommands) {
		const commands = [
			vscode.commands.registerCommand("nextEdit.toggle", () => {
				const currentState = nextEditService ? true : false
				nextEditService?.setEnabled(!currentState)
				vscode.window.showInformationMessage(`Next Edit ${!currentState ? "enabled" : "disabled"}`)
			}),
			vscode.commands.registerCommand("nextEdit.applyCurrent", () => {
				// This will be handled by the UI provider
			}),
			vscode.commands.registerCommand("nextEdit.ignoreCurrent", () => {
				// This will be handled by the UI provider
			}),
			vscode.commands.registerCommand("nextEdit.nextSuggestion", () => {
				nextEditService?.navigateToNextSuggestion()
			}),
			vscode.commands.registerCommand("nextEdit.previousSuggestion", () => {
				nextEditService?.navigateToPreviousSuggestion()
			}),
			vscode.commands.registerCommand("nextEdit.explainCurrent", () => {
				// This will be handled by the UI provider
			}),
			vscode.commands.registerCommand("nextEdit.showOptions", () => {
				// This command is triggered by status bar click
			}),
		]

		// Add commands to context subscriptions
		context.subscriptions.push(...commands)
	}

	return nextEditService
}

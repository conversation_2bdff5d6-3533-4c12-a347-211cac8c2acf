import * as vscode from "vscode"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../api"
import { NextEditContext, NextEditAIResponse, NextEditSuggestion } from "./types/NextEditTypes"

/**
 * Engine for generating Next Edit recommendations using AI
 */
export class NextEditRecommendationEngine {
	private apiHandler: ApiHandler | null = null

	constructor(apiHandler?: ApiHandler) {
		this.apiHandler = apiHandler || null
	}

	/**
	 * Generate Next Edit suggestions using AI
	 */
	async generateSuggestions(context: NextEditContext): Promise<NextEditSuggestion[]> {
		if (!this.apiHandler) {
			console.warn("NextEditRecommendationEngine: No API handler available")
			return []
		}

		try {
			const prompt = await this.buildPrompt(context)
			console.log("🚀🔍 NextEdit: Generating suggestions for", context.filePath)

			const systemPrompt =
				"You are a code assistant that provides intelligent suggestions for the next logical code modifications."
			const messages = [
				{
					role: "user" as const,
					content: prompt,
				},
			]

			// Record detailed time statistics
			const requestStartTime = Date.now()
			const requestStartISO = new Date().toISOString()

			// Print request content to console
			console.log("🚀📤 NextEdit Request:")
			console.log("System Prompt:", systemPrompt)
			console.log("User Prompt:", prompt)
			console.log(`🚀⏰ NextEdit: Starting API request at ${requestStartISO}`)
			console.log(`🚀📊 NextEdit: Request timestamp: ${requestStartTime}`)

			// Start model call
			const modelCallStartTime = Date.now()
			console.log(`🚀🤖 NextEdit: Model call started at ${new Date(modelCallStartTime).toISOString()}`)

			const stream = this.apiHandler.createMessage(systemPrompt, messages)
			let responseContent = ""
			let firstChunkTime: number | null = null
			let lastChunkTime: number | null = null
			let chunkCount = 0

			// Collect all text chunks from the stream with timing
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					const chunkTime = Date.now()
					if (firstChunkTime === null) {
						firstChunkTime = chunkTime
						const timeToFirstChunk = chunkTime - modelCallStartTime
						console.log(`🚀⚡ NextEdit: First chunk received after ${timeToFirstChunk}ms`)
					}
					lastChunkTime = chunkTime
					chunkCount++
					responseContent += chunk.text
				}
			}

			// 计算各种时间指标
			const modelCallEndTime = Date.now()
			const totalModelTime = modelCallEndTime - modelCallStartTime
			const totalRequestTime = modelCallEndTime - requestStartTime
			const timeToFirstChunk = firstChunkTime ? firstChunkTime - modelCallStartTime : 0
			const streamingTime = lastChunkTime && firstChunkTime ? lastChunkTime - firstChunkTime : 0

			console.log(`🚀⏰ NextEdit: Model call completed at ${new Date(modelCallEndTime).toISOString()}`)
			console.log(`🚀📊 NextEdit: Performance Metrics:`)
			console.log(`   📏 Total request time: ${totalRequestTime}ms`)
			console.log(`   🤖 Model processing time: ${totalModelTime}ms`)
			console.log(`   ⚡ Time to first chunk: ${timeToFirstChunk}ms`)
			console.log(`   📡 Streaming time: ${streamingTime}ms`)
			console.log(`   📦 Total chunks received: ${chunkCount}`)
			console.log(`   📝 Response length: ${responseContent.length} characters`)

			if (totalModelTime > 0) {
				const throughput = (responseContent.length / totalModelTime) * 1000 // chars per second
				console.log(`   🚀 Throughput: ${throughput.toFixed(1)} chars/sec`)
			}

			console.log("🚀📥 NextEdit Response:", responseContent)

			// Parse response time
			const parseStartTime = Date.now()
			const suggestions = this.parseAIResponse(responseContent, context.filePath)
			const parseTime = Date.now() - parseStartTime

			console.log(`🚀🔧 NextEdit: Response parsing took ${parseTime}ms`)
			console.log(`🚀✨ NextEdit: Generated ${suggestions.length} suggestions`)
			console.log(`🚀📊 NextEdit: Total end-to-end time: ${Date.now() - requestStartTime}ms`)

			return suggestions
		} catch (error) {
			console.error("🚀NextEditRecommendationEngine: Failed to generate suggestions:", error)
			return []
		}
	}

	/**
	 * Build the prompt for AI model
	 */
	private buildPrompt(context: NextEditContext): string {
		const promptTemplate = this.getBasicPromptTemplate()

		return promptTemplate
			.replace("{FILE_PATH}", context.filePath)
			.replace("{LANGUAGE}", context.language)
			.replace("{RECENT_CHANGES}", context.recentChanges)
			.replace("{CURRENT_CODE}", context.currentCode)
			.replace("{CURSOR_LINE}", context.cursorPosition?.line?.toString() || "")
			.replace("{CURSOR_COLUMN}", context.cursorPosition?.character?.toString() || "")
			.replace("{ADDITIONAL_CONTEXT}", context.additionalContext || "")
			.replace("{ERRORS_WARNINGS}", context.errorsWarnings || "")
			.replace("{ALLOWED_TASK_TYPES}", context.allowedTaskTypes || "")
	}

	/**
	 * Get basic prompt template as fallback
	 */
	private getBasicPromptTemplate(): string {
		return `## Input Context
**File Path**: \`{FILE_PATH}\`
**Programming Language**: \`{LANGUAGE}\`
**Recent Changes Made**:
\`\`\`
{RECENT_CHANGES}
\`\`\`

**Current Code**:
\`\`\`{LANGUAGE}
{CURRENT_CODE}
\`\`\`

**Cursor Position**: Line {CURSOR_LINE}, Column {CURSOR_COLUMN}
**Additional Context**: {ADDITIONAL_CONTEXT}
**Error/Warning Context**: {ERRORS_WARNINGS}

## Task

Analyze the code and recent changes, then suggest 0-3 logical next edits that would improve, complete, or fix the current implementation.

## Analysis Principles
**Before suggesting any changes:**
1. **Respect user intent**: If a user just made a change, assume it was intentional unless there's clear evidence of an error (syntax errors, broken references, etc.)
2. **Validate anchors**: Ensure anchor patterns match the EXACT text in the code, character-for-character

3. **Look for actual problems**: Only suggest fixes for real issues like:
   - Syntax errors
   - Broken references (undefined variables, missing imports)
   - Logic errors
   - Incomplete implementations
   - Security vulnerabilities

## Analysis Process
1. **Recent Change Assessment**: 
   - What did the user just change?
   - Is there evidence this was unintentional (typos, syntax errors)?
   - Are there broken dependencies caused by this change?

2. **Impact Analysis**:
   - Does the recent change break anything else in the code?
   - Are there missing corresponding updates needed?
   - Is there incomplete implementation that needs finishing?

3. **Suggestion Criteria**:
   - Only suggest changes that fix actual errors or complete partial implementations
   - Do NOT suggest reverting user changes unless there's clear breakage
   - Focus on downstream effects, not the change itself

**Important Guidelines:**
- If user just renamed something, look for OTHER places that need updating to match
- Don't suggest reverting intentional changes
- Empty suggestions array is often the correct response
- When in doubt, suggest nothing rather than potentially wrong changes

## Self-Validation Checklist
Before outputting suggestions, verify:
- [ ] Does my anchor exist exactly as written in the provided code?
- [ ] Am I respecting the user's recent intentional changes?
- [ ] Is there a real problem that needs fixing, not just a style preference?
- [ ] Would my suggestion break something or improve something?

## Output Format

Respond with a JSON object containing an array of suggestions:

\`\`\`json
{
  "suggestions": [
    {
      "id": "unique_id",
      "type": "add|modify|delete",
      "description": "Change: oldValue ➜ newValue, 
      "location": {
        "anchor": "unique code pattern to locate the position",
        "position": "before|after|replace"
      },
      "patch": {
        "old_content": "exact content to be replaced (for modify/delete)",
        "new_content": "new content to insert/replace with (for add/modify)"
      }
    }
  ]
}
\`\`\`

**Description Format Requirements:**
- Always use the format: "Change: oldValue ➜ newValue"
- For add operations: "Change: Add newValue"
- For delete operations: "Change: Delete oldValue"
- For modify operations: "Change: oldValue ➜ newValue"
- Use concise, meaningful names for oldValue and newValue

**Example Descriptions:**
- "Change: addEvent ➜ addNewEvent"
- "Change: Add error handling"
- "Change: var ➜ const"
- "Change: function ➜ async function"

**Anchor guidelines:**
- Use specific, unique text patterns that are at least 8-15 characters long
- Include enough context to make the pattern unique (function names + parameters, class names + methods, etc.)
- For functions: include function name and part of parameters, e.g., "function calculateTotal(items, tax)"
- For classes: include class name and method, e.g., "class UserService { validateEmail"
- For variables: include surrounding context, e.g., "const apiEndpoint = 'https://api"
- For HTML: include tag with attributes, e.g., "div class=\"container\" id=\"main\""
- Avoid overly generic patterns like "return", "if", "const", etc.
- Choose patterns that appear only once in the target location
- Test that the anchor is specific enough to uniquely identify the location

Be conservative and only suggest changes that are clearly beneficial and safe.`
	}

	/**
	 * Parse AI response and convert to NextEditSuggestion objects
	 */
	private parseAIResponse(content: string, filePath: string): NextEditSuggestion[] {
		try {
			// Extract JSON from response (handle markdown code blocks)
			const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/```\s*([\s\S]*?)\s*```/)
			const jsonStr = jsonMatch ? jsonMatch[1] : content

			const response: NextEditAIResponse = JSON.parse(jsonStr)

			if (!response.suggestions || !Array.isArray(response.suggestions)) {
				console.warn("NextEditRecommendationEngine: Invalid response format")
				return []
			}

			// Filter and process suggestions
			return response.suggestions
				.filter((suggestion) => {
					// Basic validation - ensure required fields exist
					return suggestion.id && suggestion.type && suggestion.description && suggestion.location
				})
				.map((suggestion) => {
					// Fix field name mismatches between AI response and TypeScript interface
					const { reasoning, ...suggestionWithoutReasoning } = suggestion as any
					const fixedSuggestion = {
						...suggestionWithoutReasoning,
						filePath,
						createdAt: new Date(),
					}

					// Convert location field names from AI response format to TypeScript interface format
					if (suggestion.location) {
						const location = suggestion.location as any
						fixedSuggestion.location = {
							anchor: location.anchor || "",
							position: location.position || "after",
						}
					}

					// Convert patch field names from AI response format to TypeScript interface format
					if (suggestion.patch) {
						const patch = suggestion.patch as any
						fixedSuggestion.patch = {
							oldContent: patch.old_content || patch.oldContent || "",
							newContent: patch.new_content || patch.newContent || "",
						}
					}

					console.log(`🔧 NextEdit: Fixed suggestion ${fixedSuggestion.id}:`, {
						type: fixedSuggestion.type,
						description: fixedSuggestion.description,
						anchor: fixedSuggestion.location?.anchor,
						position: fixedSuggestion.location?.position,
						hasOldContent: !!fixedSuggestion.patch?.oldContent,
						hasNewContent: !!fixedSuggestion.patch?.newContent,
					})

					return fixedSuggestion as NextEditSuggestion
				})
		} catch (error) {
			console.error("🚀NextEditRecommendationEngine: Failed to parse AI response:", error)
			console.error("Response content:", content)
			return []
		}
	}

	/**
	 * Update API handler
	 */
	updateApiHandler(apiHandler: ApiHandler | null): void {
		this.apiHandler = apiHandler
		console.log("🚀🔧 NextEdit: RecommendationEngine API handler updated:", !!this.apiHandler)
	}
}

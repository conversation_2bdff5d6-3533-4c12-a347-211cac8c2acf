/**
 * Test script to verify the specific multi-line anchor case from user's issue
 *
 * This script tests the exact anchor pattern that was failing:
 * "hourCell.addEventListener('click', () => {\n                    const dateTime = new Date(date);\n                    dateTime.setHours(hour, 0, 0, 0);\n                    this.showAddEventModal(dateTime);"
 */

const fs = require("fs")
const path = require("path")

function testSpecificMultiLineAnchor() {
	console.log("🔍 Testing Specific Multi-line Anchor Case...\n")

	// Create a mock document content that contains the anchor
	const mockDocumentContent = `
class CalendarView {
    renderWeekView() {
        const hourCell = document.createElement('div');
        hourCell.addEventListener('click', () => {
            const dateTime = new Date(date);
            dateTime.setHours(hour, 0, 0, 0);
            this.showAddEventModal(dateTime);
        });
        return hourCell;
    }

    renderDayView() {
        const hourCell = document.createElement('div');
        hourCell.addEventListener('click', () => {
            const dateTime = new Date(this.currentDate);
            dateTime.setHours(hour, 0, 0, 0);
            this.showAddEventModal(dateTime);
        });
        return hourCell;
    }
}
`.trim()

	// Test data from user's issue
	const testSuggestions = [
		{
			id: "update_week_view_click_handler",
			type: "modify",
			description: "Change: this.showAddEventModal ➜ this.showAddNewEventModal",
			location: {
				anchor: "hourCell.addEventListener('click', () => {\n                    const dateTime = new Date(date);\n                    dateTime.setHours(hour, 0, 0, 0);\n                    this.showAddEventModal(dateTime);",
				position: "replace"
			},
			patch: {
				old_content: "this.showAddEventModal(dateTime);",
				new_content: "this.showAddNewEventModal(dateTime);"
			}
		},
		{
			id: "update_day_view_click_handler",
			type: "modify",
			description: "Change: this.showAddEventModal ➜ this.showAddNewEventModal",
			location: {
				anchor: "hourCell.addEventListener('click', () => {\n                const dateTime = new Date(this.currentDate);\n                dateTime.setHours(hour, 0, 0, 0);\n                this.showAddEventModal(dateTime);",
				position: "replace"
			},
			patch: {
				old_content: "this.showAddEventModal(dateTime);",
				new_content: "this.showAddNewEventModal(dateTime);"
			}
		}
	]

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify multi-line anchor detection works
	results.total++
	try {
		const anchor1 = testSuggestions[0].location.anchor
		const anchor2 = testSuggestions[1].location.anchor

		const hasNewlines1 = anchor1.includes('\n')
		const hasNewlines2 = anchor2.includes('\n')

		if (hasNewlines1 && hasNewlines2) {
			results.passed++
			results.tests.push({ name: "Multi-line anchors contain newlines", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchors contain newlines", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchors contain newlines", status: "ERROR", error: error.message })
	}

	// Test 2: Verify anchor content can be found in document (manual simulation)
	results.total++
	try {
		const anchor1 = testSuggestions[0].location.anchor
		const anchor2 = testSuggestions[1].location.anchor

		// Normalize whitespace for comparison
		const normalizeWhitespace = (text) => text.replace(/\s+/g, ' ').trim()
		
		const normalizedDoc = normalizeWhitespace(mockDocumentContent)
		const normalizedAnchor1 = normalizeWhitespace(anchor1)
		const normalizedAnchor2 = normalizeWhitespace(anchor2)

		const found1 = normalizedDoc.includes(normalizedAnchor1)
		const found2 = normalizedDoc.includes(normalizedAnchor2)

		if (found1 && found2) {
			results.passed++
			results.tests.push({ name: "Anchors can be found in document content", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ 
				name: "Anchors can be found in document content", 
				status: "FAIL",
				details: `Anchor1 found: ${found1}, Anchor2 found: ${found2}`
			})
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Anchors can be found in document content", status: "ERROR", error: error.message })
	}

	// Test 3: Verify anchor lines split correctly
	results.total++
	try {
		const anchor1 = testSuggestions[0].location.anchor
		const lines1 = anchor1.split('\n')

		const expectedLines = [
			"hourCell.addEventListener('click', () => {",
			"                    const dateTime = new Date(date);",
			"                    dateTime.setHours(hour, 0, 0, 0);",
			"                    this.showAddEventModal(dateTime);"
		]

		const hasCorrectLineCount = lines1.length === expectedLines.length
		const firstLineMatches = lines1[0].trim() === expectedLines[0].trim()
		const lastLineMatches = lines1[lines1.length - 1].trim() === expectedLines[expectedLines.length - 1].trim()

		if (hasCorrectLineCount && firstLineMatches && lastLineMatches) {
			results.passed++
			results.tests.push({ name: "Anchor lines split correctly", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ 
				name: "Anchor lines split correctly", 
				status: "FAIL",
				details: `Line count: ${lines1.length}, First line match: ${firstLineMatches}, Last line match: ${lastLineMatches}`
			})
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Anchor lines split correctly", status: "ERROR", error: error.message })
	}

	// Test 4: Verify implementation handles the specific case
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that the implementation has all necessary components
		const hasMultiLineDetection = content.includes("anchorPattern.includes('\\n')")
		const hasMultiLineMethod = content.includes("findMultiLineAnchor")
		const hasFlexibleMatching = content.includes("tryFlexibleMultiLineMatch")
		const hasNormalizedComparison = content.includes("replace(/\\s+/g, ' ')")

		if (hasMultiLineDetection && hasMultiLineMethod && hasFlexibleMatching && hasNormalizedComparison) {
			results.passed++
			results.tests.push({ name: "Implementation has all required components", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ 
				name: "Implementation has all required components", 
				status: "FAIL",
				details: `Detection: ${hasMultiLineDetection}, Method: ${hasMultiLineMethod}, Flexible: ${hasFlexibleMatching}, Normalized: ${hasNormalizedComparison}`
			})
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Implementation has all required components", status: "ERROR", error: error.message })
	}

	// Print results
	console.log("📊 Test Results:")
	results.tests.forEach((test) => {
		const status = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`  ${status} ${test.name}`)
		if (test.error) {
			console.log(`    Error: ${test.error}`)
		}
		if (test.details) {
			console.log(`    Details: ${test.details}`)
		}
	})

	console.log(`\n📈 Summary: ${results.passed}/${results.total} tests passed`)
	console.log(`🎯 Result: ${results.passed === results.total ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`)

	// Print sample anchor for debugging
	console.log("\n🔍 Sample anchor pattern:")
	console.log(testSuggestions[0].location.anchor)
	console.log("\n🔍 Anchor lines:")
	testSuggestions[0].location.anchor.split('\n').forEach((line, i) => {
		console.log(`  ${i + 1}: "${line}"`)
	})

	return results.passed === results.total
}

// Run the test
if (require.main === module) {
	testSpecificMultiLineAnchor()
}

module.exports = { testSpecificMultiLineAnchor }

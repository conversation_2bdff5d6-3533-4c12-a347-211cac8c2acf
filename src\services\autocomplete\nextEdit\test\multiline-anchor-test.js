/**
 * Test script to verify multi-line anchor matching functionality
 *
 * This script validates that:
 * 1. Multi-line anchors can be correctly matched in documents
 * 2. Different matching strategies work (exact, trimmed, flexible)
 * 3. Position calculations are correct for multi-line matches
 */

const fs = require("fs")
const path = require("path")

function validateMultiLineAnchorMatching() {
	console.log("🔍 Validating NextEdit Multi-line Anchor Matching...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify findMultiLineAnchor method exists
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasMultiLineMethod =
			content.includes("findMultiLineAnchor") &&
			content.includes("private findMultiLineAnchor") &&
			content.includes("anchorPattern.includes('\\n')")

		if (hasMultiLineMethod) {
			results.passed++
			results.tests.push({ name: "findMultiLineAnchor method exists", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "findMultiLineAnchor method exists", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "findMultiLineAnchor method exists", status: "ERROR", error: error.message })
	}

	// Test 2: Verify multi-line detection in findSuggestionLocation
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasMultiLineDetection =
			content.includes("anchorPattern.includes('\\n')") &&
			content.includes("Multi-line anchor detected") &&
			content.includes("using multi-line matching")

		if (hasMultiLineDetection) {
			results.passed++
			results.tests.push({ name: "Multi-line anchor detection", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchor detection", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchor detection", status: "ERROR", error: error.message })
	}

	// Test 3: Verify exact multi-line matching strategy
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasExactMatching =
			content.includes("tryExactMultiLineMatch") &&
			content.includes("Found exact multi-line match") &&
			content.includes("anchorLine.trim()")

		if (hasExactMatching) {
			results.passed++
			results.tests.push({ name: "Exact multi-line matching strategy", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Exact multi-line matching strategy", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Exact multi-line matching strategy", status: "ERROR", error: error.message })
	}

	// Test 4: Verify trimmed multi-line matching strategy
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasTrimmedMatching =
			content.includes("tryTrimmedMultiLineMatch") &&
			content.includes("Found trimmed multi-line match") &&
			content.includes("?.trim()")

		if (hasTrimmedMatching) {
			results.passed++
			results.tests.push({ name: "Trimmed multi-line matching strategy", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Trimmed multi-line matching strategy", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Trimmed multi-line matching strategy", status: "ERROR", error: error.message })
	}

	// Test 5: Verify flexible whitespace matching strategy
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasFlexibleMatching =
			content.includes("tryFlexibleMultiLineMatch") &&
			content.includes("Found flexible multi-line match") &&
			content.includes("replace(/\\s+/g, ' ')")

		if (hasFlexibleMatching) {
			results.passed++
			results.tests.push({ name: "Flexible whitespace matching strategy", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Flexible whitespace matching strategy", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Flexible whitespace matching strategy", status: "ERROR", error: error.message })
	}

	// Test 6: Verify multi-line range calculation
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasRangeCalculation =
			content.includes("calculateMultiLineRange") &&
			content.includes("startLine + lineCount - 1") &&
			content.includes("case \"before\"") &&
			content.includes("case \"after\"")

		if (hasRangeCalculation) {
			results.passed++
			results.tests.push({ name: "Multi-line range calculation", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line range calculation", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line range calculation", status: "ERROR", error: error.message })
	}

	// Test 7: Verify logging for multi-line anchors
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasMultiLineLogging =
			content.includes("Searching for multi-line anchor with") &&
			content.includes("anchorLines.length") &&
			content.includes("Could not find multi-line anchor pattern")

		if (hasMultiLineLogging) {
			results.passed++
			results.tests.push({ name: "Multi-line anchor logging", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchor logging", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchor logging", status: "ERROR", error: error.message })
	}

	// Print results
	console.log("📊 Test Results:")
	results.tests.forEach((test) => {
		const status = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`  ${status} ${test.name}`)
		if (test.error) {
			console.log(`    Error: ${test.error}`)
		}
	})

	console.log(`\n📈 Summary: ${results.passed}/${results.total} tests passed`)
	console.log(`🎯 Result: ${results.passed === results.total ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`)

	return results.passed === results.total
}

// Run the validation
if (require.main === module) {
	validateMultiLineAnchorMatching()
}

module.exports = { validateMultiLineAnchorMatching }

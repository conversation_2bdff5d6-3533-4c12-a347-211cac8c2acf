import * as vscode from "vscode"
import { NextEditSuggestion, NextEditEventCallback, NextEditEvent, NextEditEventType } from "./types/NextEditTypes"

/**
 * Provides UI components for Next Edit suggestions
 */
export class NextEditUIProvider {
	private decorationType: vscode.TextEditorDecorationType
	private statusBarItem: vscode.StatusBarItem
	private eventCallback?: NextEditEventCallback
	private currentSuggestion: NextEditSuggestion | null = null
	private currentEditor: vscode.TextEditor | null = null
	private disposables: vscode.Disposable[] = []
	private hoverProvider: vscode.Disposable | null = null
	private currentSuggestions: NextEditSuggestion[] = []
	private currentSuggestionIndex: number = 0
	private currentHoverRange: vscode.Range | null = null

	// Anchor detection and hover functionality
	private anchorRanges: Map<string, { range: vscode.Range; suggestion: NextEditSuggestion }> = new Map()
	private isHoveringAnchor: boolean = false
	private hoverTimeout: NodeJS.Timeout | null = null
	private context?: vscode.ExtensionContext

	constructor() {
		// Create decoration type for highlighting suggestion locations
		this.decorationType = vscode.window.createTextEditorDecorationType({
			backgroundColor: new vscode.ThemeColor("editor.selectionBackground"),
			border: "2px solid",
			borderColor: new vscode.ThemeColor("editor.selectionHighlightBorder"),
			borderRadius: "4px",
		})

		// Create status bar item for navigation
		this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99)
		this.statusBarItem.hide()

		// Register commands for user interaction
		this.registerCommands()

		// Setup ESC key handling
		this.setupEscapeHandler()

		// Setup anchor hover detection
		this.setupAnchorHoverDetection()
	}

	/**
	 * Register commands for user interaction
	 */
	private registerCommands(): void {
		// Command to apply current suggestion (Ctrl+;)
		this.disposables.push(
			vscode.commands.registerCommand("nextEdit.applyCurrent", async () => {
				try {
					await this.handleApplySuggestion()
				} catch (error) {
					console.error(`NextEdit: Apply command failed:`, error)
					vscode.window.showErrorMessage(`NextEdit Apply failed: ${error}`)
				}
			}),
		)

		// Command to ignore current suggestion
		this.disposables.push(
			vscode.commands.registerCommand("nextEdit.ignoreCurrent", async () => {
				await this.handleIgnoreSuggestion()
			}),
		)

		// Command to go to next suggestion
		this.disposables.push(
			vscode.commands.registerCommand("nextEdit.nextSuggestion", () => {
				this.handleNextSuggestion()
			}),
		)

		// Command to explain current suggestion
		this.disposables.push(
			vscode.commands.registerCommand("nextEdit.explainCurrent", () => {
				this.handleExplainSuggestion()
			}),
		)

		// Debug command to test hover content
		this.disposables.push(
			vscode.commands.registerCommand("nextEdit.debugHoverContent", () => {
				this.debugHoverContent()
			}),
		)
	}

	/**
	 * Show suggestion at specific location
	 */
	async showSuggestion(suggestion: NextEditSuggestion, currentIndex: number, totalSuggestions: number): Promise<void> {
		const editor = vscode.window.activeTextEditor
		if (!editor || editor.document.uri.fsPath !== suggestion.filePath) {
			return
		}

		// Find the location in the document
		const location = await this.findSuggestionLocation(editor.document, suggestion)
		if (!location) {
			console.warn("NextEditUIProvider: Could not find suggestion location")
			return
		}

		// Navigate to the location and select the code
		await this.navigateToLocation(editor, location)

		// Store current hover range and editor
		this.currentHoverRange = location
		this.currentSuggestion = suggestion
		this.currentEditor = editor

		// Show subtle highlight decoration
		this.showHighlightDecoration(editor, location)

		// Show floating panel using hover provider
		this.showFloatingPanel(suggestion, currentIndex, totalSuggestions)

		// Update status bar
		this.updateStatusBar(suggestion, currentIndex, totalSuggestions)
	}

	/**
	 * Setup anchor hover detection for automatic highlighting and UI display
	 */
	private setupAnchorHoverDetection(): void {
		// Listen for cursor position changes
		const cursorListener = vscode.window.onDidChangeTextEditorSelection((event) => {
			this.handleCursorPositionChange(event)
		})
		this.disposables.push(cursorListener)

		// Listen for active editor changes
		const editorListener = vscode.window.onDidChangeActiveTextEditor((editor) => {
			if (editor) {
				this.handleEditorChange(editor)
			}
		})
		this.disposables.push(editorListener)
	}

	/**
	 * Handle cursor position changes to detect anchor hovering
	 */
	private handleCursorPositionChange(event: vscode.TextEditorSelectionChangeEvent): void {
		const editor = event.textEditor
		if (!editor || this.anchorRanges.size === 0) {
			return
		}

		const cursorPosition = event.selections[0].active

		// Check if cursor is within any anchor range
		const anchorInfo = this.findAnchorAtPosition(editor, cursorPosition)

		if (anchorInfo) {
			this.handleAnchorHover(editor, anchorInfo.range, anchorInfo.suggestion)
		} else if (this.isHoveringAnchor) {
			this.handleAnchorLeave()
		}
	}

	/**
	 * Handle active editor changes
	 */
	private handleEditorChange(editor: vscode.TextEditor): void {
		// Clear anchor ranges for previous editor
		this.anchorRanges.clear()
		this.isHoveringAnchor = false

		// Clear any existing hover timeout
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
			this.hoverTimeout = null
		}
	}

	/**
	 * Find anchor at the given position
	 */
	private findAnchorAtPosition(
		editor: vscode.TextEditor,
		position: vscode.Position,
	): { range: vscode.Range; suggestion: NextEditSuggestion } | null {
		this.anchorRanges.forEach((anchorInfo, anchorId) => {
			if (anchorInfo.range.contains(position)) {
				return anchorInfo
			}
		})

		// Alternative approach using Array.from
		const anchorEntries = Array.from(this.anchorRanges.entries())
		for (const [anchorId, anchorInfo] of anchorEntries) {
			if (anchorInfo.range.contains(position)) {
				return anchorInfo
			}
		}
		return null
	}

	/**
	 * Handle anchor hover - show highlighting and UI
	 */
	private handleAnchorHover(editor: vscode.TextEditor, range: vscode.Range, suggestion: NextEditSuggestion): void {
		if (this.isHoveringAnchor) {
			return // Already hovering
		}

		this.isHoveringAnchor = true

		// Clear any existing hover timeout
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
		}

		// Add a small delay to avoid flickering
		this.hoverTimeout = setTimeout(() => {
			// Set current state
			this.currentSuggestion = suggestion
			this.currentEditor = editor
			this.currentHoverRange = range

			// Show highlighting
			this.showHighlightDecoration(editor, range)

			// Show floating UI
			this.showFloatingPanel(suggestion, 0, 1)
		}, 200) // 200ms delay to avoid flickering
	}

	/**
	 * Handle anchor leave - hide highlighting and UI
	 */
	private handleAnchorLeave(): void {
		this.isHoveringAnchor = false

		// Clear hover timeout
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
			this.hoverTimeout = null
		}

		// Add a delay before clearing to avoid flickering when moving within the same anchor
		setTimeout(() => {
			if (!this.isHoveringAnchor) {
				console.log(`🎯 NextEdit: Clearing anchor hover`)
				this.clearUI()
			}
		}, 100) // 100ms delay
	}

	/**
	 * Show suggestions in floating panel
	 */
	async showSuggestions(suggestions: NextEditSuggestion[]): Promise<void> {
		console.log(`📋 NextEdit: Received ${suggestions.length} suggestions, filtering valid ones...`)

		// Filter out suggestions that can't find their anchor pattern
		const validSuggestions = await this.filterValidSuggestions(suggestions)

		console.log(`📋 NextEdit: ${validSuggestions.length} valid suggestions after filtering`)

		this.currentSuggestions = validSuggestions
		this.currentSuggestionIndex = 0

		// Register anchor ranges for hover detection
		await this.registerAnchorRanges(validSuggestions)

		if (validSuggestions.length > 0) {
			await this.showSuggestion(validSuggestions[0], 0, validSuggestions.length)
		} else {
			console.warn(`⚠️ NextEdit: No valid suggestions found after filtering`)
		}
	}

	/**
	 * Register anchor ranges for hover detection
	 */
	private async registerAnchorRanges(suggestions: NextEditSuggestion[]): Promise<void> {
		const editor = vscode.window.activeTextEditor
		if (!editor) {
			console.warn(`⚠️ NextEdit: No active editor for registering anchor ranges`)
			return
		}

		// Clear existing anchor ranges
		this.anchorRanges.clear()

		console.log(`🎯 NextEdit: Registering ${suggestions.length} anchor ranges`)

		for (const suggestion of suggestions) {
			// Skip suggestions for different files
			if (editor.document.uri.fsPath !== suggestion.filePath) {
				continue
			}

			// Find the anchor location
			const location = await this.findSuggestionLocation(editor.document, suggestion)
			if (location) {
				const anchorId = `${suggestion.id}_${location.start.line}_${location.start.character}`
				this.anchorRanges.set(anchorId, {
					range: location,
					suggestion: suggestion,
				})

				console.log(
					`🎯 NextEdit: Registered anchor range for "${suggestion.description}" at ${location.start.line}:${location.start.character}-${location.end.line}:${location.end.character}`,
				)
			}
		}

		console.log(`🎯 NextEdit: Total ${this.anchorRanges.size} anchor ranges registered`)
	}

	/**
	 * Filter suggestions to only include those with valid anchor patterns
	 */
	private async filterValidSuggestions(suggestions: NextEditSuggestion[]): Promise<NextEditSuggestion[]> {
		const editor = vscode.window.activeTextEditor
		if (!editor) {
			console.warn(`⚠️ NextEdit: No active editor for filtering suggestions`)
			return []
		}

		const validSuggestions: NextEditSuggestion[] = []

		for (const suggestion of suggestions) {
			// Skip suggestions for different files
			if (editor.document.uri.fsPath !== suggestion.filePath) {
				console.log(`🔍 NextEdit: Skipping suggestion ${suggestion.id} - different file`)
				continue
			}

			// Check if anchor pattern is too short (less than 8 characters)
			const anchorPattern = suggestion.location.anchor?.trim()
			if (!anchorPattern || anchorPattern.length < 8) {
				console.warn(`⚠️ NextEdit: Discarding suggestion ${suggestion.id} - anchor too short: "${anchorPattern}"`)
				continue
			}

			// Check if anchor and new_content are identical (no actual change)
			const newContent = suggestion.patch.newContent?.trim()
			if (anchorPattern === newContent) {
				console.warn(
					`⚠️ NextEdit: Discarding suggestion ${suggestion.id} - anchor and new_content are identical: "${anchorPattern}"`,
				)
				continue
			}

			// For modify suggestions, check if oldContent and newContent are identical
			if (suggestion.type === "modify" && suggestion.patch.oldContent && suggestion.patch.newContent) {
				const oldContent = suggestion.patch.oldContent.trim()
				const trimmedNewContent = suggestion.patch.newContent.trim()
				if (oldContent === trimmedNewContent) {
					console.warn(
						`⚠️ NextEdit: Discarding suggestion ${suggestion.id} - oldContent and newContent are identical: "${oldContent}"`,
					)
					continue
				}
			}

			// Try to find the anchor pattern in the document
			const location = await this.findSuggestionLocation(editor.document, suggestion)
			if (location) {
				console.log(`✅ NextEdit: Valid suggestion ${suggestion.id} - anchor found`)
				validSuggestions.push(suggestion)
			} else {
				console.warn(`❌ NextEdit: Discarding suggestion ${suggestion.id} - anchor not found: "${anchorPattern}"`)
			}
		}

		return validSuggestions
	}

	/**
	 * Find the location of a suggestion in the document
	 */
	private async findSuggestionLocation(
		document: vscode.TextDocument,
		suggestion: NextEditSuggestion,
	): Promise<vscode.Range | null> {
		const text = document.getText()
		const lines = text.split("\n")

		console.log(`🔍 NextEdit: Finding location for suggestion ${suggestion.id}`)
		console.log(`📍 Anchor pattern: "${suggestion.location.anchor}"`)
		console.log(`📍 Position: ${suggestion.location.position}`)

		// Validate location data
		if (!suggestion.location.anchor) {
			console.error(`❌ NextEdit: No anchor pattern provided for suggestion ${suggestion.id}`)
			return null
		}

		// Try to find the anchor pattern
		const anchorPattern = suggestion.location.anchor.trim()
		let foundMatches = 0

		console.log(`🔍 NextEdit: Processing anchor pattern: "${anchorPattern}"`)

		// Check if anchor contains newlines (multi-line anchor)
		if (anchorPattern.includes('\n')) {
			console.log(`🔍 NextEdit: Multi-line anchor detected, using multi-line matching`)
			return this.findMultiLineAnchor(document, suggestion, anchorPattern)
		}

		// Single-line anchor matching (existing logic)
		for (let i = 0; i < lines.length; i++) {
			const line = lines[i]

			// Try multiple matching strategies with enhanced escape character support
			const matchResult = this.findAnchorInLine(line, anchorPattern)

			if (matchResult.isMatch) {
				foundMatches++
				console.log(`🎯 NextEdit: Found anchor at line ${i + 1}: "${line.trim()}"`)
				console.log(`🔍 NextEdit: Matched using strategy: ${matchResult.strategy}`)
				console.log(`📍 NextEdit: Match position: ${matchResult.startChar}-${matchResult.endChar}`)

				// Determine the exact position based on the position type and suggestion type
				return this.calculatePreciseRange(suggestion, i, lines, matchResult.startChar, matchResult.endChar)
			}
		}

		console.error(`❌ NextEdit: Could not find anchor pattern "${anchorPattern}" in document`)
		console.error(`📊 NextEdit: Document has ${lines.length} lines, found ${foundMatches} potential matches`)
		console.error(`💡 NextEdit: Suggestion will be discarded due to missing anchor`)

		return null
	}

	/**
	 * Find multi-line anchor pattern in the document
	 */
	private findMultiLineAnchor(
		document: vscode.TextDocument,
		suggestion: NextEditSuggestion,
		anchorPattern: string,
	): vscode.Range | null {
		const text = document.getText()
		const lines = text.split("\n")
		const anchorLines = anchorPattern.split('\n')

		console.log(`🔍 NextEdit: Searching for multi-line anchor with ${anchorLines.length} lines`)

		// Try different matching strategies for multi-line anchors
		for (let startLine = 0; startLine <= lines.length - anchorLines.length; startLine++) {
			// Strategy 1: Exact multi-line match
			const exactMatch = this.tryExactMultiLineMatch(lines, anchorLines, startLine)
			if (exactMatch) {
				console.log(`🎯 NextEdit: Found exact multi-line match at line ${startLine + 1}`)
				return this.calculateMultiLineRange(suggestion, startLine, anchorLines.length, exactMatch)
			}

			// Strategy 2: Trimmed multi-line match (ignore leading/trailing whitespace)
			const trimmedMatch = this.tryTrimmedMultiLineMatch(lines, anchorLines, startLine)
			if (trimmedMatch) {
				console.log(`🎯 NextEdit: Found trimmed multi-line match at line ${startLine + 1}`)
				return this.calculateMultiLineRange(suggestion, startLine, anchorLines.length, trimmedMatch)
			}

			// Strategy 3: Flexible whitespace multi-line match
			const flexibleMatch = this.tryFlexibleMultiLineMatch(lines, anchorLines, startLine)
			if (flexibleMatch) {
				console.log(`🎯 NextEdit: Found flexible multi-line match at line ${startLine + 1}`)
				return this.calculateMultiLineRange(suggestion, startLine, anchorLines.length, flexibleMatch)
			}
		}

		console.error(`❌ NextEdit: Could not find multi-line anchor pattern in document`)
		return null
	}

	/**
	 * Try exact multi-line match
	 */
	private tryExactMultiLineMatch(
		documentLines: string[],
		anchorLines: string[],
		startLine: number,
	): { startChar: number; endChar: number } | null {
		for (let i = 0; i < anchorLines.length; i++) {
			const docLine = documentLines[startLine + i]
			const anchorLine = anchorLines[i]

			if (!docLine || !docLine.includes(anchorLine.trim())) {
				return null
			}
		}

		// Found exact match
		const firstLine = documentLines[startLine]
		const lastLine = documentLines[startLine + anchorLines.length - 1]
		const firstAnchorLine = anchorLines[0].trim()
		const lastAnchorLine = anchorLines[anchorLines.length - 1].trim()

		const startChar = firstLine.indexOf(firstAnchorLine)
		const endChar = lastLine.indexOf(lastAnchorLine) + lastAnchorLine.length

		return { startChar: Math.max(0, startChar), endChar }
	}

	/**
	 * Try trimmed multi-line match (ignore leading/trailing whitespace)
	 */
	private tryTrimmedMultiLineMatch(
		documentLines: string[],
		anchorLines: string[],
		startLine: number,
	): { startChar: number; endChar: number } | null {
		for (let i = 0; i < anchorLines.length; i++) {
			const docLine = documentLines[startLine + i]?.trim()
			const anchorLine = anchorLines[i].trim()

			if (!docLine || !docLine.includes(anchorLine)) {
				return null
			}
		}

		// Found trimmed match
		const firstLine = documentLines[startLine]
		const lastLine = documentLines[startLine + anchorLines.length - 1]
		const firstAnchorLine = anchorLines[0].trim()
		const lastAnchorLine = anchorLines[anchorLines.length - 1].trim()

		const startChar = firstLine.indexOf(firstAnchorLine)
		const endChar = lastLine.indexOf(lastAnchorLine) + lastAnchorLine.length

		return { startChar: Math.max(0, startChar), endChar }
	}

	/**
	 * Try flexible whitespace multi-line match
	 */
	private tryFlexibleMultiLineMatch(
		documentLines: string[],
		anchorLines: string[],
		startLine: number,
	): { startChar: number; endChar: number } | null {
		for (let i = 0; i < anchorLines.length; i++) {
			const docLine = documentLines[startLine + i]
			const anchorLine = anchorLines[i]

			if (!docLine) {
				return null
			}

			// Normalize whitespace for comparison
			const normalizedDocLine = docLine.replace(/\s+/g, ' ').trim()
			const normalizedAnchorLine = anchorLine.replace(/\s+/g, ' ').trim()

			if (!normalizedDocLine.includes(normalizedAnchorLine)) {
				return null
			}
		}

		// Found flexible match
		const firstLine = documentLines[startLine]
		const lastLine = documentLines[startLine + anchorLines.length - 1]
		const firstAnchorLine = anchorLines[0].trim()
		const lastAnchorLine = anchorLines[anchorLines.length - 1].trim()

		const startChar = firstLine.indexOf(firstAnchorLine)
		const endChar = lastLine.indexOf(lastAnchorLine) + lastAnchorLine.length

		return { startChar: Math.max(0, startChar), endChar }
	}

	/**
	 * Calculate range for multi-line anchor match
	 */
	private calculateMultiLineRange(
		suggestion: NextEditSuggestion,
		startLine: number,
		lineCount: number,
		matchInfo: { startChar: number; endChar: number },
	): vscode.Range {
		const position = suggestion.location.position
		const endLine = startLine + lineCount - 1

		switch (position) {
			case "before":
				// Insert before the matched anchor
				return new vscode.Range(
					new vscode.Position(startLine, 0),
					new vscode.Position(startLine, 0)
				)

			case "after":
				// Insert after the matched anchor
				const lastLineLength = vscode.window.activeTextEditor?.document.lineAt(endLine).text.length || 0
				return new vscode.Range(
					new vscode.Position(endLine, lastLineLength),
					new vscode.Position(endLine, lastLineLength)
				)

			case "replace":
			default:
				// Replace the matched anchor
				return new vscode.Range(
					new vscode.Position(startLine, matchInfo.startChar),
					new vscode.Position(endLine, matchInfo.endChar)
				)
		}
	}

	/**
	 * Find anchor pattern in a line with support for escape characters
	 */
	private findAnchorInLine(
		line: string,
		anchorPattern: string,
	): {
		isMatch: boolean
		startChar: number
		endChar: number
		strategy: string
	} {
		// Strategy 1: Exact literal match (fastest)
		let index = line.indexOf(anchorPattern)
		if (index !== -1) {
			return {
				isMatch: true,
				startChar: index,
				endChar: index + anchorPattern.length,
				strategy: "exact_literal",
			}
		}

		// Strategy 2: Unescaped pattern matching (handle common escape sequences)
		const unescapedPattern = this.unescapeAnchorPattern(anchorPattern)
		if (unescapedPattern !== anchorPattern) {
			index = line.indexOf(unescapedPattern)
			if (index !== -1) {
				return {
					isMatch: true,
					startChar: index,
					endChar: index + unescapedPattern.length,
					strategy: "unescaped_pattern",
				}
			}
		}

		// Strategy 3: Regex-based matching for complex patterns
		try {
			const regexPattern = this.convertAnchorToRegex(anchorPattern)
			const match = line.match(regexPattern)
			if (match && match.index !== undefined) {
				return {
					isMatch: true,
					startChar: match.index,
					endChar: match.index + match[0].length,
					strategy: "regex_pattern",
				}
			}
		} catch (error) {
			console.log(`🔍 NextEdit: Regex pattern failed for "${anchorPattern}": ${error}`)
		}

		// Strategy 4: HTML/XML tag matching for complex patterns
		if (anchorPattern.includes("=") && anchorPattern.includes('"')) {
			const htmlMatch = this.findHtmlTagMatch(line, anchorPattern)
			if (htmlMatch.isMatch) {
				return {
					...htmlMatch,
					strategy: "html_tag_match",
				}
			}
		}

		// Strategy 5: Fuzzy matching (remove quotes and extra spaces)
		const cleanPattern = anchorPattern.replace(/["']/g, "").replace(/\s+/g, " ").trim()
		const cleanLine = line.replace(/["']/g, "").replace(/\s+/g, " ")
		index = cleanLine.indexOf(cleanPattern)
		if (index !== -1) {
			// Map back to original line position (approximate)
			const originalIndex = this.mapCleanIndexToOriginal(line, cleanLine, index)
			return {
				isMatch: true,
				startChar: originalIndex,
				endChar: originalIndex + cleanPattern.length,
				strategy: "fuzzy_match",
			}
		}

		return {
			isMatch: false,
			startChar: -1,
			endChar: -1,
			strategy: "no_match",
		}
	}

	/**
	 * Unescape common escape sequences in anchor patterns
	 */
	private unescapeAnchorPattern(pattern: string): string {
		return pattern
			.replace(/\\"/g, '"') // \" -> "
			.replace(/\\'/g, "'") // \' -> '
			.replace(/\\\\/g, "\\") // \\ -> \
			.replace(/\\n/g, "\n") // \n -> newline
			.replace(/\\t/g, "\t") // \t -> tab
			.replace(/\\r/g, "\r") // \r -> carriage return
	}

	/**
	 * Convert anchor pattern to regex for complex matching
	 */
	private convertAnchorToRegex(pattern: string): RegExp {
		// Escape special regex characters except for intentional patterns
		let regexPattern = pattern
			.replace(/[.*+?^${}()|[\]\\]/g, "\\$&") // Escape special chars
			.replace(/\\\\/g, "\\\\") // Handle escaped backslashes
			.replace(/\\"/g, '"') // Unescape quotes
			.replace(/\\'/g, "'") // Unescape single quotes

		// Allow for flexible whitespace matching
		regexPattern = regexPattern.replace(/\s+/g, "\\s+")

		return new RegExp(regexPattern, "i") // Case insensitive
	}

	/**
	 * Find HTML tag matches for complex patterns with multiple attributes
	 */
	private findHtmlTagMatch(
		line: string,
		pattern: string,
	): {
		isMatch: boolean
		startChar: number
		endChar: number
	} {
		// For patterns like: div id="globe-container" class="globe-container"
		// Try to find the complete tag that contains all the specified attributes

		// Extract tag name from pattern
		const tagNameMatch = pattern.match(/^(\w+)\s/)
		if (!tagNameMatch) {
			return { isMatch: false, startChar: -1, endChar: -1 }
		}

		const tagName = tagNameMatch[1]

		// Extract all attributes from pattern
		const attrRegex = /(\w+)\s*=\s*["']([^"']+)["']/g
		const attrMatches: RegExpExecArray[] = []
		let match: RegExpExecArray | null
		while ((match = attrRegex.exec(pattern)) !== null) {
			attrMatches.push(match)
		}
		if (attrMatches.length === 0) {
			return { isMatch: false, startChar: -1, endChar: -1 }
		}

		// Find tag opening in the line
		const tagRegex = new RegExp(`<${tagName}\\b[^>]*>`, "i")
		const lineTagMatch = line.match(tagRegex)

		if (!lineTagMatch || lineTagMatch.index === undefined) {
			return { isMatch: false, startChar: -1, endChar: -1 }
		}

		const tagContent = lineTagMatch[0]

		// Check if all attributes from pattern exist in the tag
		for (const [, attrName, attrValue] of attrMatches) {
			const attrRegex = new RegExp(
				`\\b${attrName}\\s*=\\s*["']([^"']*${attrValue.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}[^"']*)["']`,
				"i",
			)
			if (!attrRegex.test(tagContent)) {
				return { isMatch: false, startChar: -1, endChar: -1 }
			}
		}

		// All attributes found, return the tag range
		return {
			isMatch: true,
			startChar: lineTagMatch.index,
			endChar: lineTagMatch.index + tagContent.length,
		}
	}

	/**
	 * Find attribute-based matches for HTML/XML patterns (legacy method)
	 */
	private findAttributeMatch(
		line: string,
		pattern: string,
	): {
		isMatch: boolean
		startChar: number
		endChar: number
	} {
		// Parse pattern like: class="globe-container-xxx"
		const attrMatch = pattern.match(/(\w+)\s*=\s*["']([^"']+)["']/)
		if (!attrMatch) {
			return { isMatch: false, startChar: -1, endChar: -1 }
		}

		const [, attrName, attrValue] = attrMatch

		// Look for the attribute in the line
		const attrRegex = new RegExp(
			`\\b${attrName}\\s*=\\s*["']([^"']*${attrValue.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}[^"']*)["']`,
			"i",
		)
		const match = line.match(attrRegex)

		if (match && match.index !== undefined) {
			return {
				isMatch: true,
				startChar: match.index,
				endChar: match.index + match[0].length,
			}
		}

		return { isMatch: false, startChar: -1, endChar: -1 }
	}

	/**
	 * Map cleaned string index back to original string position
	 */
	private mapCleanIndexToOriginal(original: string, cleaned: string, cleanIndex: number): number {
		let originalIndex = 0
		let cleanedIndex = 0

		while (cleanedIndex < cleanIndex && originalIndex < original.length) {
			const originalChar = original[originalIndex]

			// Skip quotes and normalize spaces in original
			if (originalChar === '"' || originalChar === "'") {
				originalIndex++
				continue
			}

			if (/\s/.test(originalChar)) {
				// Skip extra whitespace in original
				while (originalIndex < original.length && /\s/.test(original[originalIndex])) {
					originalIndex++
				}
				cleanedIndex++
				continue
			}

			originalIndex++
			cleanedIndex++
		}

		return Math.max(0, originalIndex)
	}

	/**
	 * Calculate precise range for suggestion application
	 */
	private calculatePreciseRange(
		suggestion: NextEditSuggestion,
		lineIndex: number,
		lines: string[],
		matchStartChar: number,
		matchEndChar: number,
	): vscode.Range {
		const line = lines[lineIndex]

		switch (suggestion.location.position) {
			case "replace":
				// For replace operations, we need to determine what exactly to replace
				if (suggestion.type === "modify" && suggestion.patch.oldContent) {
					// Try to find the old content to replace precisely
					const oldContent = suggestion.patch.oldContent.trim()
					const oldContentIndex = line.indexOf(oldContent)

					if (oldContentIndex !== -1) {
						// Found exact old content, replace only that part
						const startPos = new vscode.Position(lineIndex, oldContentIndex)
						const endPos = new vscode.Position(lineIndex, oldContentIndex + oldContent.length)
						console.log(
							`🎯 NextEdit: Precise replace range: ${lineIndex + 1}:${oldContentIndex} to ${lineIndex + 1}:${oldContentIndex + oldContent.length}`,
						)
						return new vscode.Range(startPos, endPos)
					}
				}

				// Fallback: replace the matched anchor text
				if (matchStartChar !== -1 && matchEndChar !== -1) {
					const startPos = new vscode.Position(lineIndex, matchStartChar)
					const endPos = new vscode.Position(lineIndex, matchEndChar)
					console.log(
						`🎯 NextEdit: Replace anchor range: ${lineIndex + 1}:${matchStartChar} to ${lineIndex + 1}:${matchEndChar}`,
					)
					return new vscode.Range(startPos, endPos)
				}

				// Last fallback: replace entire line
				const startPos = new vscode.Position(lineIndex, 0)
				const endPos = new vscode.Position(lineIndex, line.length)
				return new vscode.Range(startPos, endPos)

			case "after":
				// Insert after the matched content
				const afterPos = new vscode.Position(lineIndex, matchEndChar !== -1 ? matchEndChar : line.length)
				return new vscode.Range(afterPos, afterPos)

			case "before":
				// Insert before the matched content
				const beforePos = new vscode.Position(lineIndex, matchStartChar !== -1 ? matchStartChar : 0)
				return new vscode.Range(beforePos, beforePos)

			default:
				// Default to end of line for insertion
				const defaultPos = new vscode.Position(lineIndex, line.length)
				return new vscode.Range(defaultPos, defaultPos)
		}
	}

	/**
	 * Navigate to the specified location and select the code
	 */
	private async navigateToLocation(editor: vscode.TextEditor, location: vscode.Range): Promise<void> {
		// Select the entire range that will be modified
		editor.selection = new vscode.Selection(location.start, location.end)

		// Reveal the range
		editor.revealRange(location, vscode.TextEditorRevealType.InCenterIfOutsideViewport)
	}

	/**
	 * Show floating panel with suggestion details using Hover Provider
	 */
	private showFloatingPanel(suggestion: NextEditSuggestion, currentIndex: number, totalSuggestions: number): void {
		console.log(`🎈 NextEdit: Showing floating panel for suggestion ${currentIndex + 1}/${totalSuggestions}`)
		console.log(`🎈 NextEdit: Suggestion type: ${suggestion.type}, description: ${suggestion.description}`)

		// Clear existing hover provider
		if (this.hoverProvider) {
			console.log(`🎈 NextEdit: Disposing existing hover provider`)
			this.hoverProvider.dispose()
		}

		// Store current hover range
		if (!this.currentHoverRange) {
			console.log(`🎈 NextEdit: No current hover range, cannot show panel`)
			return
		}

		console.log(
			`🎈 NextEdit: Hover range: ${this.currentHoverRange.start.line}:${this.currentHoverRange.start.character}-${this.currentHoverRange.end.line}:${this.currentHoverRange.end.character}`,
		)

		// Create hover provider for the specific range
		this.hoverProvider = vscode.languages.registerHoverProvider(
			{ scheme: "file" },
			{
				provideHover: (document, position) => {
					console.log(`🎈 NextEdit: Hover provider called at position ${position.line}:${position.character}`)
					console.log(
						`🎈 NextEdit: Current hover range: ${this.currentHoverRange?.start.line}:${this.currentHoverRange?.start.character}-${this.currentHoverRange?.end.line}:${this.currentHoverRange?.end.character}`,
					)

					// Only show hover for the current suggestion range
					if (!this.currentHoverRange || !this.currentHoverRange.contains(position)) {
						console.log(`🎈 NextEdit: Position not in hover range, returning null`)
						return null
					}

					console.log(`🎈 NextEdit: Creating hover content for suggestion: ${suggestion.description}`)

					// Create hover content with action buttons
					const hoverContent = this.createHoverContent(suggestion, currentIndex, totalSuggestions)

					console.log(`🎈 NextEdit: Generated hover content length: ${hoverContent.value.length}`)
					console.log(`🎈 NextEdit: Hover content preview: ${hoverContent.value.substring(0, 100)}...`)

					return new vscode.Hover(hoverContent, this.currentHoverRange)
				},
			},
		)

		// Trigger hover by moving cursor to the range
		if (this.currentEditor) {
			// Set cursor to the start of the range to trigger hover
			this.currentEditor.selection = new vscode.Selection(this.currentHoverRange.start, this.currentHoverRange.start)

			// Execute hover command to show the hover
			vscode.commands.executeCommand("editor.action.showHover")
		}
	}

	/**
	 * Create hover content with action buttons and code diff
	 */
	private createHoverContent(
		suggestion: NextEditSuggestion,
		currentIndex: number,
		totalSuggestions: number,
	): vscode.MarkdownString {
		const markdown = new vscode.MarkdownString()
		markdown.isTrusted = true
		markdown.supportHtml = true // Try HTML support outside code blocks

		// Extension icon and action buttons using qax icon
		const iconUri = this.getQaxIconUri()
		if (iconUri) {
			markdown.appendMarkdown(
				`<img src="${iconUri}" width="16" height="16" style="vertical-align: middle;">&nbsp;&nbsp;&nbsp;&nbsp;`,
			)
		} else {
			markdown.appendMarkdown(`🔧&nbsp;&nbsp;&nbsp;&nbsp;`) // Fallback emoji
		}
		markdown.appendMarkdown(`[Apply](command:nextEdit.applyCurrent "Apply Suggestion")`)
		markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
		markdown.appendMarkdown(`[Ignore](command:nextEdit.ignoreCurrent "Ignore Suggestion")`)
		markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
		markdown.appendMarkdown(`[Next](command:nextEdit.nextSuggestion "Next Suggestion")`)
		markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
		markdown.appendMarkdown(`[Explain](command:nextEdit.explainCurrent "Explain Suggestion")`)

		// Show counter if multiple suggestions
		if (totalSuggestions > 1) {
			markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;*(${currentIndex + 1}/${totalSuggestions})*`)
		}

		// Separator
		markdown.appendMarkdown(`\n\n---\n\n`)

		// Show suggestion description
		const description = suggestion.description || "Code modification"
		markdown.appendMarkdown(`**${description}**\n\n`)

		// Show the actual code diff with HTML coloring
		const codeDiff = this.generateColoredDiff(suggestion)
		if (codeDiff) {
			markdown.appendMarkdown(codeDiff)
		} else {
			markdown.appendMarkdown(`*No code changes to display*`)
		}

		return markdown
	}

	/**
	 * Generate colored diff display using VSCode theme variables and codicons
	 */
	private generateColoredDiff(suggestion: NextEditSuggestion): string {
		// Ensure we have patch data
		if (!suggestion.patch) {
			return `*No patch data available*`
		}

		const oldContent = suggestion.patch.oldContent || ""
		const newContent = suggestion.patch.newContent || ""

		// Get the line number and context from the anchor
		const { startLine, contextLines } = this.getLineNumberAndContext(suggestion.location?.anchor || "", suggestion.filePath)

		let result = `<pre><span style="background-color:var(--vscode-editor-background);">`

		// Add context before (if available)
		if (contextLines.length > 0 && startLine > 1) {
			const prevLine = contextLines[startLine - 2] || ""
			result += `<span style="color:var(--vscode-editorGhostText-foreground);"> ${(startLine - 1).toString().padStart(3)} ${(startLine - 1).toString().padStart(3)}<span class="codicon codicon-blank"></span>   ${this.escapeHtml(prevLine)}</span>\n`
		}

		switch (suggestion.type) {
			case "add":
				// Add new content with VSCode insert background
				if (newContent.trim()) {
					const newLines = newContent.split("\n")
					newLines.forEach((line, index) => {
						const lineNum = startLine + index
						result += `<span style="color:var(--vscode-editorGhostText-foreground);background-color:var(--vscode-diffEditor-insertedLineBackground);">     ${lineNum.toString().padStart(3)}</span>`
						result += `<span style="background-color:var(--vscode-diffEditor-insertedLineBackground);"><span class="codicon codicon-diff-insert"></span> </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-insertedLineBackground);">${this.escapeHtml(line)}</span>\n`
					})
				}
				break

			case "modify":
				// Remove old content, then add new content
				if (oldContent.trim()) {
					const oldLines = oldContent.split("\n")
					oldLines.forEach((line, index) => {
						const lineNum = startLine + index
						result += `<span style="color:var(--vscode-editorGhostText-foreground);background-color:var(--vscode-diffEditor-removedLineBackground);"> ${lineNum.toString().padStart(3)}    </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-removedLineBackground);"><span class="codicon codicon-diff-remove"></span> </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-removedLineBackground);">${this.escapeHtml(line)}</span>\n`
					})
				}
				if (newContent.trim()) {
					const newLines = newContent.split("\n")
					newLines.forEach((line, index) => {
						const lineNum = startLine + index
						result += `<span style="color:var(--vscode-editorGhostText-foreground);background-color:var(--vscode-diffEditor-insertedLineBackground);">     ${lineNum.toString().padStart(3)}</span>`
						result += `<span style="background-color:var(--vscode-diffEditor-insertedLineBackground);"><span class="codicon codicon-diff-insert"></span> </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-insertedLineBackground);">${this.escapeHtml(line)}</span>\n`
					})
				}
				break

			case "delete":
				// Remove content with VSCode remove background
				if (oldContent.trim()) {
					const oldLines = oldContent.split("\n")
					oldLines.forEach((line, index) => {
						const lineNum = startLine + index
						result += `<span style="color:var(--vscode-editorGhostText-foreground);background-color:var(--vscode-diffEditor-removedLineBackground);"> ${lineNum.toString().padStart(3)}    </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-removedLineBackground);"><span class="codicon codicon-diff-remove"></span> </span>`
						result += `<span style="background-color:var(--vscode-diffEditor-removedLineBackground);">${this.escapeHtml(line)}</span>\n`
					})
				}
				break
		}

		// Add context after (if available)
		const maxLines = Math.max(oldContent.split("\n").length, newContent.split("\n").length)
		const nextLineIndex = startLine + maxLines - 1
		if (contextLines.length > nextLineIndex) {
			const nextLine = contextLines[nextLineIndex] || ""
			result += `<span style="color:var(--vscode-editorGhostText-foreground);"> ${(nextLineIndex + 1).toString().padStart(3)} ${(nextLineIndex + 1).toString().padStart(3)}<span class="codicon codicon-blank"></span> ${this.escapeHtml(nextLine)}</span>`
		}

		result += `</span></pre>`
		return result
	}

	/**
	 * Escape HTML characters for safe display
	 */
	private escapeHtml(text: string): string {
		return text
			.replace(/&/g, "&amp;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;")
			.replace(/"/g, "&quot;")
			.replace(/'/g, "&#39;")
	}

	/**
	 * Get line number and context lines from anchor and file content
	 */
	private getLineNumberAndContext(anchor: string, filePath: string): { startLine: number; contextLines: string[] } {
		try {
			// Try to get the active editor and find the line number
			const editor = vscode.window.activeTextEditor
			if (editor && editor.document.uri.fsPath === filePath) {
				const document = editor.document
				const text = document.getText()
				const lines = text.split("\n")

				// Find the line that contains the anchor
				for (let i = 0; i < lines.length; i++) {
					if (lines[i].includes(anchor.trim())) {
						return {
							startLine: i + 1, // VSCode uses 1-based line numbers
							contextLines: lines,
						}
					}
				}
			}
		} catch (error) {
			console.warn(`Failed to get line number from anchor: ${error}`)
		}

		// Fallback to a reasonable default
		return {
			startLine: 208,
			contextLines: [],
		}
	}

	/**
	 * Get programming language from file path for syntax highlighting
	 */
	private getLanguageFromFilePath(filePath: string): string {
		const extension = filePath.split(".").pop()?.toLowerCase()

		const languageMap: { [key: string]: string } = {
			ts: "typescript",
			js: "javascript",
			tsx: "typescript",
			jsx: "javascript",
			py: "python",
			java: "java",
			cpp: "cpp",
			c: "c",
			cs: "csharp",
			php: "php",
			rb: "ruby",
			go: "go",
			rs: "rust",
			swift: "swift",
			kt: "kotlin",
			html: "html",
			css: "css",
			scss: "scss",
			json: "json",
			xml: "xml",
			yaml: "yaml",
			yml: "yaml",
			md: "markdown",
		}

		return languageMap[extension || ""] || "text"
	}

	/**
	 * Setup ESC key handling
	 */
	private setupEscapeHandler(): void {
		// Register a unique command for ESC handling
		const escapeCommand = `nextEdit.escape.${Date.now()}`
		const disposable = vscode.commands.registerCommand(escapeCommand, () => {
			this.clearUI()
		})
		this.disposables.push(disposable)

		// Listen for significant cursor movements (not just any selection change)
		const keyListener = vscode.window.onDidChangeTextEditorSelection((event) => {
			// Only clear UI if hover is active and cursor moves significantly away
			if (this.hoverProvider && this.currentHoverRange && this.currentEditor) {
				const editor = event.textEditor
				if (editor === this.currentEditor) {
					const selection = event.selections[0]

					// Only clear if cursor moves more than 3 lines away from hover range
					const hoverStartLine = this.currentHoverRange.start.line
					const hoverEndLine = this.currentHoverRange.end.line
					const cursorLine = selection.active.line

					const distanceFromHover = Math.min(Math.abs(cursorLine - hoverStartLine), Math.abs(cursorLine - hoverEndLine))

					// Clear UI only if cursor moves significantly away (more than 3 lines)
					if (distanceFromHover > 3) {
						console.log(`🔄 NextEdit: Cursor moved ${distanceFromHover} lines away, clearing UI`)
						this.clearUI()
					}
				}
			}
		})
		this.disposables.push(keyListener)
	}

	/**
	 * Show code diff visualization with appropriate highlighting
	 */
	private showHighlightDecoration(editor: vscode.TextEditor, location: vscode.Range): void {
		console.log(
			`🎨 NextEdit: Showing highlight decoration at range ${location.start.line}:${location.start.character}-${location.end.line}:${location.end.character}`,
		)

		// Clear any existing decorations first
		this.clearDecorations(editor)

		// Show diff-specific highlighting based on suggestion type
		if (this.currentSuggestion) {
			console.log(`🎨 NextEdit: Using suggestion type "${this.currentSuggestion.type}" for highlighting`)
			this.showCodeDiffHighlight(editor, location, this.currentSuggestion)
		} else {
			console.log(`🎨 NextEdit: Using fallback basic highlighting`)
			// Fallback to basic highlighting
			editor.setDecorations(this.decorationType, [location])
		}

		console.log(`🎨 NextEdit: Highlight decoration applied successfully`)
	}

	/**
	 * Show code diff highlighting based on suggestion type
	 */
	private showCodeDiffHighlight(editor: vscode.TextEditor, location: vscode.Range, suggestion: NextEditSuggestion): void {
		console.log(
			`🎨 NextEdit: Applying ${suggestion.type} highlighting to range ${location.start.line}:${location.start.character}-${location.end.line}:${location.end.character}`,
		)

		// Use basic highlighting for all suggestion types
		console.log(`🎨 NextEdit: Setting ${suggestion.type.toUpperCase()} decoration`)
		editor.setDecorations(this.decorationType, [location])

		console.log(`🎨 NextEdit: Decoration applied for ${suggestion.type} suggestion`)
	}

	/**
	 * Clear all decorations
	 */
	private clearDecorations(editor: vscode.TextEditor): void {
		editor.setDecorations(this.decorationType, [])
	}

	/**
	 * Handle apply suggestion action
	 */
	private async handleApplySuggestion(): Promise<void> {
		console.log(`🚀 NextEdit: handleApplySuggestion called`)
		console.log(`🚀 NextEdit: Current suggestion:`, this.currentSuggestion?.description)
		console.log(`🚀 NextEdit: Current editor:`, this.currentEditor?.document.uri.fsPath)

		// Store references before clearing UI
		const suggestion = this.currentSuggestion
		const editor = this.currentEditor

		if (suggestion && editor) {
			console.log(`🚀 NextEdit: Both suggestion and editor available, proceeding with apply`)

			// Mark file as recently applied BEFORE making changes
			this.emitEvent({
				type: NextEditEventType.SUGGESTION_APPLIED,
				filePath: suggestion.filePath,
				suggestionId: suggestion.id,
				timestamp: new Date(),
			})

			// Clear UI immediately to provide instant feedback
			console.log(`🚀 NextEdit: Clearing UI immediately`)
			this.clearUI()

			try {
				await this.applySuggestionToEditor(suggestion, editor)

				console.log(`✅ NextEdit: Applied suggestion: ${suggestion.description}`)

				// Remove the applied suggestion from the list
				this.currentSuggestions.splice(this.currentSuggestionIndex, 1)

				// Adjust current index if needed
				if (this.currentSuggestionIndex >= this.currentSuggestions.length) {
					this.currentSuggestionIndex = 0
				}

				// Clear and re-register anchor ranges for remaining suggestions
				await this.registerAnchorRanges(this.currentSuggestions)

				// After applying, move to next suggestion if available
				if (this.currentSuggestions.length > 0) {
					console.log(`🔄 NextEdit: Moving to next suggestion after apply`)
					// Wait a bit for the edit to be processed
					setTimeout(() => {
						const nextSuggestion = this.currentSuggestions[this.currentSuggestionIndex]
						this.showSuggestion(nextSuggestion, this.currentSuggestionIndex, this.currentSuggestions.length)
					}, 500)
				} else {
					console.log(`🏁 NextEdit: No more suggestions available after apply`)
					this.clearAllSuggestions()
					vscode.window.showInformationMessage("Next Edit suggestion applied successfully!")
				}
			} catch (error) {
				console.error(`❌ NextEdit: Failed to apply suggestion:`, error)
				vscode.window.showErrorMessage(`Failed to apply Next Edit suggestion: ${error}`)
			}
		} else {
			console.warn(`⚠️ NextEdit: Cannot apply suggestion - missing data:`)
			console.warn(`   Current suggestion: ${!!suggestion}`)
			console.warn(`   Current editor: ${!!editor}`)
			if (!suggestion) {
				console.warn(`   No current suggestion available`)
			}
			if (!editor) {
				console.warn(`   No current editor available`)
			}
		}
	}

	/**
	 * Apply suggestion to the editor
	 */
	private async applySuggestionToEditor(suggestion: NextEditSuggestion, editor: vscode.TextEditor): Promise<void> {
		const document = editor.document

		console.log(`🔧 NextEdit: Applying suggestion ${suggestion.id} of type "${suggestion.type}"`)

		// Find the location where the suggestion should be applied
		const location = await this.findSuggestionLocation(document, suggestion)
		if (!location) {
			throw new Error(`Could not find suggestion location in document. Anchor: "${suggestion.location.anchor}"`)
		}

		console.log(
			`📍 NextEdit: Applying at range ${location.start.line + 1}:${location.start.character} to ${location.end.line + 1}:${location.end.character}`,
		)

		// Validate patch content
		if (!suggestion.patch) {
			throw new Error("Suggestion has no patch information")
		}

		// Create workspace edit
		const edit = new vscode.WorkspaceEdit()

		// Apply the suggestion based on its type
		switch (suggestion.type) {
			case "add":
				if (!suggestion.patch.newContent) {
					throw new Error("Add suggestion has no new content")
				}
				console.log(`➕ NextEdit: Adding content: "${suggestion.patch.newContent.substring(0, 50)}..."`)
				// Insert new content at the specified location
				edit.insert(document.uri, location.start, suggestion.patch.newContent + "\n")
				break

			case "modify":
				if (!suggestion.patch.newContent) {
					throw new Error("Modify suggestion has no new content")
				}
				console.log(`🔄 NextEdit: Replacing content with: "${suggestion.patch.newContent.substring(0, 50)}..."`)
				// Replace the range with new content
				edit.replace(document.uri, location, suggestion.patch.newContent)
				break

			case "delete":
				console.log(`🗑️ NextEdit: Deleting range`)
				// Delete the specified range
				edit.delete(document.uri, location)
				break

			default:
				throw new Error(`Unknown suggestion type: ${suggestion.type}`)
		}

		// Apply the edit
		console.log(`💾 NextEdit: Applying workspace edit...`)
		const success = await vscode.workspace.applyEdit(edit)
		if (!success) {
			throw new Error("Failed to apply workspace edit - VS Code rejected the edit")
		}

		console.log(`✅ NextEdit: Successfully applied suggestion ${suggestion.id}`)
	}

	/**
	 * Handle ignore suggestion action
	 */
	private async handleIgnoreSuggestion(): Promise<void> {
		const currentIndex = this.currentSuggestionIndex

		if (this.currentSuggestion) {
			this.emitEvent({
				type: NextEditEventType.SUGGESTION_IGNORED,
				filePath: this.currentSuggestion.filePath,
				suggestionId: this.currentSuggestion.id,
				timestamp: new Date(),
			})
		}

		// Clear UI immediately
		this.clearUI()

		// Remove the ignored suggestion from the list
		this.currentSuggestions.splice(currentIndex, 1)

		// Adjust current index if needed
		if (this.currentSuggestionIndex >= this.currentSuggestions.length) {
			this.currentSuggestionIndex = 0
		}

		// Clear and re-register anchor ranges for remaining suggestions
		await this.registerAnchorRanges(this.currentSuggestions)

		// After ignoring, move to next suggestion if available
		if (this.currentSuggestions.length > 0) {
			console.log(`🔄 NextEdit: Moving to next suggestion after ignore`)
			setTimeout(() => {
				const nextSuggestion = this.currentSuggestions[this.currentSuggestionIndex]
				this.showSuggestion(nextSuggestion, this.currentSuggestionIndex, this.currentSuggestions.length)
			}, 200)
		} else {
			console.log(`🏁 NextEdit: No more suggestions available after ignore`)
			this.clearAllSuggestions()
		}
	}

	/**
	 * Handle next suggestion action
	 */
	private handleNextSuggestion(): void {
		if (this.currentSuggestions.length <= 1) {
			// Show message if there's only one suggestion
			vscode.window.showInformationMessage("This is the only suggestion available.")
			return
		}

		// Clear current UI first to avoid visual artifacts
		this.clearUI()

		// Move to next suggestion
		this.currentSuggestionIndex = (this.currentSuggestionIndex + 1) % this.currentSuggestions.length
		const nextSuggestion = this.currentSuggestions[this.currentSuggestionIndex]

		console.log(`🔄 NextEdit: Moving to suggestion ${this.currentSuggestionIndex + 1} of ${this.currentSuggestions.length}`)

		// Show the next suggestion
		this.showSuggestion(nextSuggestion, this.currentSuggestionIndex, this.currentSuggestions.length)
	}

	/**
	 * Handle explain suggestion action
	 */
	private handleExplainSuggestion(): void {
		// Clear current UI first to avoid visual artifacts
		this.clearUI()

		if (this.currentSuggestion) {
			this.showSuggestionDetails(this.currentSuggestion)
		}
	}

	/**
	 * Update status bar with suggestion info
	 */
	private updateStatusBar(suggestion: NextEditSuggestion, currentIndex: number, totalSuggestions: number): void {
		// Truncate description if it's too long
		const description = suggestion.description || ""
		const truncatedDescription = description.length > 30 ? description.substring(0, 27) + "..." : description

		this.statusBarItem.text = `$(lightbulb) Next Edit: ${currentIndex + 1}/${totalSuggestions} - ${truncatedDescription}`
		this.statusBarItem.tooltip = `${description}\n\nClick to show options`
		this.statusBarItem.command = "nextEdit.showOptions"
		this.statusBarItem.show()
	}

	/**
	 * Show detailed information about the suggestion in a comprehensive explanation panel
	 */
	private async showSuggestionDetails(suggestion: NextEditSuggestion): Promise<void> {
		const language = this.getLanguageFromFilePath(suggestion.filePath)
		const fileName = suggestion.filePath.split(/[/\\]/).pop() || suggestion.filePath

		// Generate comprehensive explanation
		const details = `# 🔍 Next Edit Suggestion Explanation

## 📁 File Information
- **File:** \`${fileName}\`
- **Full Path:** \`${suggestion.filePath}\`
- **Language:** ${language}
- **Operation Type:** **${suggestion.type.toUpperCase()}**

## 📝 What This Change Does
${suggestion.description}

## 🤔 Why This Change Is Suggested
${suggestion.reasoning}

## 🔄 Code Changes

### ${this.getChangeTypeDescription(suggestion.type)}

${this.generateDetailedCodeDiff(suggestion, language)}

## 🎯 Impact Analysis

### What Will Happen:
${this.generateImpactAnalysis(suggestion)}

### Potential Benefits:
${this.generateBenefitsAnalysis(suggestion)}

### Considerations:
${this.generateConsiderationsAnalysis(suggestion)}

## 🚀 Next Steps
1. **Review** the code changes above
2. **Apply** if the change looks good to you
3. **Ignore** if you don't want this change
4. **Next** to see other suggestions (if available)

---
*Generated by Next Edit AI Assistant*`

		const doc = await vscode.workspace.openTextDocument({
			content: details,
			language: "markdown",
		})

		await vscode.window.showTextDocument(doc, {
			viewColumn: vscode.ViewColumn.Beside,
			preview: true,
		})
	}

	/**
	 * Get description for change type
	 */
	private getChangeTypeDescription(type: string): string {
		switch (type) {
			case "add":
				return "➕ **Adding New Code**"
			case "modify":
				return "🔄 **Modifying Existing Code**"
			case "delete":
				return "🗑️ **Removing Code**"
			default:
				return "🔧 **Code Change**"
		}
	}

	/**
	 * Generate detailed code diff with better formatting
	 */
	private generateDetailedCodeDiff(suggestion: NextEditSuggestion, language: string): string {
		switch (suggestion.type) {
			case "add":
				return `**New code to be added:**
\`\`\`${language}
${suggestion.patch.newContent}
\`\`\``

			case "modify":
				if (suggestion.patch.oldContent) {
					return `**Current code:**
\`\`\`${language}
${suggestion.patch.oldContent}
\`\`\`

**Will be changed to:**
\`\`\`${language}
${suggestion.patch.newContent}
\`\`\`

**Diff view:**
\`\`\`diff
- ${suggestion.patch.oldContent}
+ ${suggestion.patch.newContent}
\`\`\``
				} else {
					return `**New code:**
\`\`\`${language}
${suggestion.patch.newContent}
\`\`\``
				}

			case "delete":
				if (suggestion.patch.oldContent) {
					return `**Code to be removed:**
\`\`\`${language}
${suggestion.patch.oldContent}
\`\`\``
				} else {
					return "*Selected content will be deleted*"
				}

			default:
				return `**Code change:**
\`\`\`${language}
${suggestion.patch.newContent}
\`\`\``
		}
	}

	/**
	 * Generate impact analysis
	 */
	private generateImpactAnalysis(suggestion: NextEditSuggestion): string {
		switch (suggestion.type) {
			case "add":
				return "- New functionality will be added to your code\n- No existing code will be modified\n- This is a safe, non-breaking change"
			case "modify":
				return "- Existing code will be updated with new implementation\n- The behavior of this code section will change\n- Please review carefully to ensure compatibility"
			case "delete":
				return "- Selected code will be permanently removed\n- Any functionality provided by this code will be lost\n- Make sure this code is no longer needed"
			default:
				return "- Your code will be modified according to the suggestion"
		}
	}

	/**
	 * Generate benefits analysis
	 */
	private generateBenefitsAnalysis(suggestion: NextEditSuggestion): string {
		// This could be enhanced with AI analysis in the future
		const benefits = []
		const reasoning = suggestion.reasoning?.toLowerCase() || ""

		if (reasoning.includes("error")) {
			benefits.push("- Improves error handling and code reliability")
		}
		if (reasoning.includes("type")) {
			benefits.push("- Enhances type safety and prevents runtime errors")
		}
		if (reasoning.includes("performance")) {
			benefits.push("- Optimizes code performance")
		}
		if (reasoning.includes("test")) {
			benefits.push("- Improves code testability and quality")
		}
		if (reasoning.includes("documentation") || reasoning.includes("comment")) {
			benefits.push("- Enhances code documentation and maintainability")
		}

		if (benefits.length === 0) {
			benefits.push("- Improves code quality and maintainability")
			benefits.push("- Follows best practices and coding standards")
		}

		return benefits.join("\n")
	}

	/**
	 * Generate considerations analysis
	 */
	private generateConsiderationsAnalysis(suggestion: NextEditSuggestion): string {
		const considerations = []

		if (suggestion.type === "delete") {
			considerations.push("- Ensure the deleted code is not used elsewhere")
			considerations.push("- Consider if any tests need to be updated")
		} else if (suggestion.type === "modify") {
			considerations.push("- Verify that the change doesn't break existing functionality")
			considerations.push("- Check if any dependent code needs updates")
		} else if (suggestion.type === "add") {
			considerations.push("- Ensure the new code fits well with existing architecture")
			considerations.push("- Consider adding tests for the new functionality")
		}

		considerations.push("- Review the change in the context of your overall codebase")
		considerations.push("- Test the change thoroughly before committing")

		return considerations.join("\n")
	}

	/**
	 * Clear all decorations and hide UI
	 */
	clearUI(): void {
		console.log(`🧹 NextEdit: Clearing UI - called from:`, new Error().stack?.split("\n")[2]?.trim())

		const editor = vscode.window.activeTextEditor
		if (editor) {
			// Clear all decorations
			console.log(`🧹 NextEdit: Clearing decorations from editor`)
			this.clearDecorations(editor)
		}

		// Clear hover provider
		if (this.hoverProvider) {
			this.hoverProvider.dispose()
			this.hoverProvider = null
		}

		this.statusBarItem.hide()
		this.currentSuggestion = null
		this.currentEditor = null
		// Don't clear currentSuggestions and currentSuggestionIndex here
		// as they are needed for navigation between suggestions
		this.currentHoverRange = null

		// Clear anchor hover state
		this.isHoveringAnchor = false
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
			this.hoverTimeout = null
		}
	}

	/**
	 * Clear all suggestions and reset state completely
	 */
	clearAllSuggestions(): void {
		this.clearUI()
		this.currentSuggestions = []
		this.currentSuggestionIndex = 0
	}

	/**
	 * Debug method to test hover content generation
	 */
	private debugHoverContent(): void {
		console.log("🐛 NextEdit: Debug hover content generation...")

		// Create a test suggestion
		const testSuggestion = {
			id: "debug_test_1",
			type: "modify" as any,
			description: "Change: addEvent ➜ addNewEvent",
			location: {
				anchor: "onClick={addEvent}",
				position: "replace" as any,
			},
			patch: {
				oldContent: "onClick={addEvent}",
				newContent: "onClick={addNewEvent}",
			},
			filePath: "debug.js",
			createdAt: new Date(),
		}

		try {
			const hoverContent = this.createHoverContent(testSuggestion, 0, 1)
			console.log("🐛 Generated hover content:")
			console.log(hoverContent.value)

			// Show in a message
			vscode.window.showInformationMessage("Debug hover content generated. Check console for details.")

			// Also show in a new document
			vscode.workspace
				.openTextDocument({
					content: hoverContent.value,
					language: "markdown",
				})
				.then((doc) => {
					vscode.window.showTextDocument(doc, { preview: true })
				})
		} catch (error) {
			console.error("🐛 Error generating hover content:", error)
			vscode.window.showErrorMessage(`Debug failed: ${error}`)
		}
	}

	/**
	 * Set event callback
	 */
	setEventCallback(callback: NextEditEventCallback): void {
		this.eventCallback = callback
	}

	/**
	 * Emit event
	 */
	private emitEvent(event: NextEditEvent): void {
		if (this.eventCallback) {
			this.eventCallback(event)
		}
	}

	/**
	 * Dispose resources
	 */
	dispose(): void {
		this.decorationType.dispose()

		// Dispose hover provider
		if (this.hoverProvider) {
			this.hoverProvider.dispose()
			this.hoverProvider = null
		}

		this.statusBarItem.dispose()
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []
		this.currentSuggestion = null
		this.currentEditor = null
		this.currentSuggestions = []
		this.currentSuggestionIndex = 0
		this.currentHoverRange = null

		// Clear anchor hover state
		this.anchorRanges.clear()
		this.isHoveringAnchor = false
		if (this.hoverTimeout) {
			clearTimeout(this.hoverTimeout)
			this.hoverTimeout = null
		}
	}

	/**
	 * Set the extension context for resource URI generation
	 */
	setContext(context: vscode.ExtensionContext): void {
		this.context = context
	}

	/**
	 * Get the URI for the qax sidebar icon
	 */
	private getQaxIconUri(): string {
		if (!this.context) {
			return "" // Fallback if context not set
		}
		const iconUri = vscode.Uri.joinPath(this.context.extensionUri, "qax", "assets", "qax-sidebar-icon.png")
		return iconUri.toString()
	}
}

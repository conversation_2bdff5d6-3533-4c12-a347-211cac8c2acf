/**
 * Integration test for multi-line anchor functionality
 *
 * This script validates that multi-line anchor matching integrates properly
 * with existing NextEdit functionality including filtering, validation, and UI.
 */

const fs = require("fs")
const path = require("path")

function runIntegrationTest() {
	console.log("🔗 Running NextEdit Multi-line Anchor Integration Test...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify multi-line anchor detection is integrated into main flow
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that findSuggestionLocation calls findMultiLineAnchor
		const hasIntegration =
			content.includes("anchorPattern.includes('\\n')") &&
			content.includes("Multi-line anchor detected") &&
			content.includes("return this.findMultiLineAnchor")

		if (hasIntegration) {
			results.passed++
			results.tests.push({ name: "Multi-line detection integrated into main flow", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line detection integrated into main flow", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line detection integrated into main flow", status: "ERROR", error: error.message })
	}

	// Test 2: Verify multi-line anchors work with existing filtering
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that filterValidSuggestions still calls findSuggestionLocation
		const hasFilteringIntegration =
			content.includes("filterValidSuggestions") &&
			content.includes("await this.findSuggestionLocation(editor.document, suggestion)") &&
			content.includes("anchor found") &&
			content.includes("anchor not found")

		if (hasFilteringIntegration) {
			results.passed++
			results.tests.push({ name: "Multi-line anchors work with existing filtering", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchors work with existing filtering", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchors work with existing filtering", status: "ERROR", error: error.message })
	}

	// Test 3: Verify multi-line anchors work with anchor range registration
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that registerAnchorRanges calls findSuggestionLocation
		const hasRangeIntegration =
			content.includes("registerAnchorRanges") &&
			content.includes("await this.findSuggestionLocation(editor.document, suggestion)") &&
			content.includes("this.anchorRanges.set")

		if (hasRangeIntegration) {
			results.passed++
			results.tests.push({ name: "Multi-line anchors work with anchor range registration", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchors work with anchor range registration", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchors work with anchor range registration", status: "ERROR", error: error.message })
	}

	// Test 4: Verify multi-line anchors work with suggestion application
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that applySuggestionToEditor calls findSuggestionLocation
		const hasApplicationIntegration =
			content.includes("applySuggestionToEditor") &&
			content.includes("await this.findSuggestionLocation(document, suggestion)") &&
			content.includes("Could not find suggestion location")

		if (hasApplicationIntegration) {
			results.passed++
			results.tests.push({ name: "Multi-line anchors work with suggestion application", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multi-line anchors work with suggestion application", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multi-line anchors work with suggestion application", status: "ERROR", error: error.message })
	}

	// Test 5: Verify backward compatibility with single-line anchors
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that single-line logic is preserved
		const hasBackwardCompatibility =
			content.includes("Single-line anchor matching (existing logic)") &&
			content.includes("findAnchorInLine") &&
			content.includes("for (let i = 0; i < lines.length; i++)")

		if (hasBackwardCompatibility) {
			results.passed++
			results.tests.push({ name: "Backward compatibility with single-line anchors", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Backward compatibility with single-line anchors", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Backward compatibility with single-line anchors", status: "ERROR", error: error.message })
	}

	// Test 6: Verify all multi-line matching strategies are implemented
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that all three strategies exist
		const hasAllStrategies =
			content.includes("tryExactMultiLineMatch") &&
			content.includes("tryTrimmedMultiLineMatch") &&
			content.includes("tryFlexibleMultiLineMatch")

		if (hasAllStrategies) {
			results.passed++
			results.tests.push({ name: "All multi-line matching strategies implemented", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "All multi-line matching strategies implemented", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "All multi-line matching strategies implemented", status: "ERROR", error: error.message })
	}

	// Test 7: Verify proper error handling for multi-line anchors
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check error handling
		const hasErrorHandling =
			content.includes("Could not find multi-line anchor pattern") &&
			content.includes("return null") &&
			content.includes("console.error")

		if (hasErrorHandling) {
			results.passed++
			results.tests.push({ name: "Proper error handling for multi-line anchors", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Proper error handling for multi-line anchors", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Proper error handling for multi-line anchors", status: "ERROR", error: error.message })
	}

	// Test 8: Verify logging consistency
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check consistent logging patterns
		const hasConsistentLogging =
			content.includes("🔍 NextEdit: Searching for multi-line anchor") &&
			content.includes("🎯 NextEdit: Found") &&
			content.includes("multi-line match at line")

		if (hasConsistentLogging) {
			results.passed++
			results.tests.push({ name: "Consistent logging patterns", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Consistent logging patterns", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Consistent logging patterns", status: "ERROR", error: error.message })
	}

	// Print results
	console.log("📊 Integration Test Results:")
	results.tests.forEach((test) => {
		const status = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`  ${status} ${test.name}`)
		if (test.error) {
			console.log(`    Error: ${test.error}`)
		}
	})

	console.log(`\n📈 Summary: ${results.passed}/${results.total} tests passed`)
	console.log(`🎯 Result: ${results.passed === results.total ? "✅ ALL INTEGRATION TESTS PASSED" : "❌ SOME INTEGRATION TESTS FAILED"}`)

	if (results.passed === results.total) {
		console.log("\n🎉 Multi-line anchor functionality is properly integrated!")
		console.log("✨ The feature should now work with the user's specific anchor patterns.")
	}

	return results.passed === results.total
}

// Run the integration test
if (require.main === module) {
	runIntegrationTest()
}

module.exports = { runIntegrationTest }
